const Job = require("../models/Job.models");
const mongoose = require("mongoose");
const User = require("../models/User.models");
const recruiterProfile = require("../models/RecruiterProfile.models");
const CoinTransaction = require("../models/CoinTransaction");
const userCoinBalance = require("../models/UserCoinBalance");
const { workOnRequest } = require("../models/WorkOnRequest.models");
const job = require("../models/Job.models");
const { bookMark } = require("../models/bookmark.models");
const { candidateSubmission } = require("../models/CandidateSubmission.models");
const { Candidates } = require("../models/Candidate.models");
const { auditLog } = require("../models/AuditLog.models");
const { submissionLog } = require("../models/submissionLog.models");

//* @Desc remove or unmap recruiter from job in aspect of not-working on it
//* @Route POST /api/v1/job/recruiter/unmap-job
//* @Access private, recruiter
const unMappedJob = async (req, res) => {
  try {
    const user = req.user;
    if (!user) {
      return res.status(400).json({
        success: false,
        message: "User not found",
      });
    }

    const { jobId } = req.body;

    const recruiter = await recruiterProfile.findOne({
      "user._id": user?._id,
    });
    if (!recruiter) {
      return res.status(404).json({
        success: false,
        message: "Recruiter not found",
      });
    }

    let isjobID = false;
    // Deactivate previous active log for the same jobId
    recruiter.jobsWorkingOn = recruiter.jobsWorkingOn?.map((entry) => {
      if (
        entry.jobId.toString() === jobId.toString() &&
        entry.isActive &&
        entry.status == "assigned" &&
        entry.assignedBy == "manager"
      ) {
        isjobID = true;
      }

      if (
        entry.jobId.toString() === jobId.toString() &&
        entry.isActive &&
        entry.status == "assigned"
      ) {
        return { ...entry, isActive: false };
      }

      return entry;
    });

    if (isjobID) {
      await workOnRequest.findOneAndUpdate(
        {
          "job._id": new mongoose.Types.ObjectId(jobId),
          "recruiter.userId": user.userId,
        },
        { status: "rejected" }
      );
    }

    // Push new assignment log
    recruiter.jobsWorkingOn.push({
      jobId,
      status: "removed",
      isActive: true,
    });
    await recruiter.save();

    return res.status(200).json({
      success: true,
      message: "Job up-mapped successfully",
    });
  } catch (err) {
    console.error("Error in unMappedJob:", err);
    return res.status(500).json({ message: err.message, status: false });
  }
};

//* @Desc get all recruiters
//* @Route POST /api/v1/headmanager/recruiter/getallrecruiters
//* @Access private, head manager
const getAllRecruiters = async (req, res) => {
  try {
    const recruiter = await User.aggregate([
      {
        $match: {
          userType: "recruiter",
        },
      },
      {
        $lookup: {
          from: "recruiters",
          localField: "userId",
          foreignField: "user.userId",
          as: "profile",
        },
      },
      {
        $unwind: {
          path: "$profile",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: "workrequests",
          localField: "userId",
          foreignField: "recruiter.userId",
          as: "workonrequests",
        },
      },
      {
        $lookup: {
          from: "candidatesubmissions",
          localField: "userId",
          foreignField: "submittedBy.userID",
          as: "submissions",
        },
      },
      {
        $addFields: {
          submissionsCount: { $size: "$submissions" },
        },
      },
      {
        $addFields: {
          workonrequestsCount: {
            $size: "$workonrequests",
          },
        },
      },
      {
        $project: {
          submissionsCount: 1,
          workonrequestsCount: 1,
          profile: 1,
          createdAt: 1,
          name: 1,
          userId: 1,
          email: 1,
          phone: 1,
          isActive: 1,
        },
      },
    ]);

    return res.status(200).json({
      message: "get all recruiters",
      success: true,
      data: recruiter,
    });
  } catch (error) {
    console.error("Error in getting recruiters:", err);
    return res.status(400).json({ message: err.message, success: false });
  }
};

//* @Desc get recruiter
//* @Route POST /api/v1/headmanager/recruiter/getrecruiter/:recruiterID
//* @Access private, head manager
const getRecruiter = async (req, res) => {
  try {
    const { recruiterID } = req.params;

    if (!recruiterID) {
      return res.status(400).json({
        success: false,
        message: "Recruiter are requires.",
      });
    }

    const recruiter = await User.aggregate([
      {
        $match: {
          userType: "recruiter",
          userId: recruiterID,
        },
      },
      {
        $lookup: {
          from: "recruiters",
          localField: "userId",
          foreignField: "user.userId",
          as: "profile",
        },
      },
      {
        $unwind: {
          path: "$profile",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: "workrequests",
          let: { userID: "$userId" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $eq: ["$recruiter.userId", "$$userID"],
                },
              },
            },
            {
              $lookup: {
                from: "jobs",
                localField: "job.jobId",
                foreignField: "jobId",
                as: "job",
              },
            },
            {
              $unwind: "$job",
            },
            {
              $lookup: {
                from: "candidatesubmissions",
                localField: "job.jobId",
                foreignField: "job.jobID",
                as: "submission",
              },
            },
            {
              $addFields: {
                submissionsCount: {
                  $size: "$submission",
                },
              },
            },
          ],
          as: "workonrequests",
        },
      },

      {
        $lookup: {
          from: "candidatesubmissions",
          let: { userID: "$userId" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $eq: ["$submittedBy.userID", "$$userID"],
                },
              },
            },
            {
              $lookup: {
                from: "jobs",
                localField: "job.jobID",
                foreignField: "jobId",
                as: "job",
              },
            },
            {
              $unwind: "$job",
            },
            {
              $lookup: {
                from: "candidates",
                localField: "candidate.candidateID",
                foreignField: "candidateID",
                as: "candidate",
              },
            },
            {
              $unwind: "$candidate",
            },
          ],
          as: "submissions",
        },
      },

      {
        $addFields: {
          submissionsCount: { $size: "$submissions" },
        },
      },
      {
        $addFields: {
          workonrequestsCount: {
            $size: "$workonrequests",
          },
        },
      },
      {
        $project: {
          submissionsCount: 1,
          workonrequestsCount: 1,
          profile: 1,
          createdAt: 1,
          name: 1,
          userId: 1,
          email: 1,
          phone: 1,
          isActive: 1,
          submissions: 1,
          workonrequests: 1,
        },
      },
    ]);

    return res.status(200).json({
      message: "get recruiters",
      success: true,
      data: recruiter,
    });
  } catch (error) {
    console.error("Error in getting recruiters:", err);
    return res.status(400).json({ message: err.message, success: false });
  }
};

//* @Desc Get matched jobs for recruiter
//* @Route GET /api/v1/recruiter/matched-jobs
//* @Access private - Recruiter
const getMatchedJobs = async (req, res) => {
  try {
    const user = req.user;
    if (!user) {
      return res.status(400).json({
        success: false,
        message: "User not found",
      });
    }
    const recruiterId = user?.userId;
    //* Verify recruiter exists
    const recruiter = await recruiterProfile.findOne({
      "user.userId": recruiterId,
    });

    if (!recruiter) {
      return res.status(404).json({
        success: false,
        message: "Recruiter not found",
      });
    }

    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    const { candidateRole, domain } = recruiter;

    //* Building match query with recruiter preferences
    const matchQuery = {
      jobProfile: { $in: candidateRole },
      industry: { $in: domain },
    };

    //* Dynamic Sorting
    const sort = {};
    if (req.query.sortBy) {
      const direction = req.query.order === "asc" ? 1 : -1;
      sort[req.query.sortBy] = direction;
    }

    const pipeline = [
      { $match: matchQuery },
      ...(Object.keys(sort).length ? [{ $sort: sort }] : []),

      {
        $lookup: {
          from: "workrequests",
          let: {
            jobId: "$_id",
            recruiterId: user.userId,
          },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$job._id", "$$jobId"] },
                    {
                      $eq: ["$recruiter.userId", "$$recruiterId"],
                    },
                    {
                      $in: ["$status", ["requestToWork", "accepted"]],
                    },
                  ],
                },
              },
            },
          ],
          as: "workrequest",
        },
      },

      {
        $lookup: {
          from: "recruiters",
          let: {
            jobId: "$_id",
            recruiterId: user.userId,
          },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    {
                      $eq: ["$user.userId", "$$recruiterId"],
                    },
                  ],
                },
              },
            },
            {
              $unwind: {
                path: "$jobsWorkingOn",
                preserveNullAndEmptyArrays: true,
              },
            },
            {
              $match: {
                $expr: {
                  $and: [
                    {
                      $eq: ["$jobsWorkingOn.jobId", "$$jobId"],
                    },
                    {
                      $eq: ["$jobsWorkingOn.isActive", true],
                    },
                    {
                      $eq: ["$jobsWorkingOn.status", "assigned"],
                    },
                  ],
                },
              },
            },
          ],
          as: "recruiter",
        },
      },
      {
        $addFields: {
          recruitercount: { $size: "$recruiter" },
          workrequestcount: { $size: "$workrequest" },
        },
      },
      {
        $match: {
          $expr: {
            $and: [
              { $eq: ["$workrequestcount", 0] },
              { $eq: ["$recruitercount", 0] },
            ],
          },
        },
      },
      {
        $lookup: {
          from: "users",
          let: {
            userId: "$accountManager._id",
          },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [{ $eq: ["$_id", "$$userId"] }],
                },
              },
            },

            {
              $project: {
                name: 1,
                email: 1,
                userId: 1,
                phone: 1,
              },
            },
          ],
          as: "accountManager",
        },
      },
      {
        $unwind: {
          path: "$accountManager",
          preserveNullAndEmptyArrays: false,
        },
      },

      {
        $lookup: {
          from: "bookmarks",
          let: {
            userId: new mongoose.Types.ObjectId(user?._id),
            jobID: "$_id",
          },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$jobId", "$$jobID"] },
                    {
                      $eq: ["$recruiterId", "$$userId"],
                    },
                  ],
                },
              },
            },
          ],
          as: "bookmark",
        },
      },
      {
        $addFields: {
          isBookmark: {
            $gt: [{ $size: "$bookmark" }, 0],
          },
        },
      },
      {
        $lookup: {
          from: "candidatesubmissions",
          let: { jobID: "$_id" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $eq: ["$job._id", "$$jobID"],
                },
              },
            },
            {
              $facet: {
                candidate: [
                  {
                    $group: {
                      _id: "$status",
                      count: {
                        $sum: 1,
                      },
                    },
                  },
                  {
                    $project: {
                      status: "$_id",
                      count: "$count",
                      _id: false,
                    },
                  },
                ],

                recruiterCount: [
                  {
                    $group: {
                      _id: "$submittedBy._id",
                    },
                  },
                  {
                    $count: "count",
                  },
                ],
              },
            },
          ],
          as: "submission",
        },
      },
      {
        $unwind: {
          path: "$submission",
          preserveNullAndEmptyArrays: false,
        },
      },
      {
        $project: {
          bookmark: 0,
        },
      },
      {
        $sort: {
          createdAt: -1,
        },
      },
      {
        $facet: {
          metadata: [{ $count: "total" }],
          data: [{ $skip: skip }, { $limit: limit }],
        },
      },
    ];

    const results = await Job.aggregate(pipeline);

    const totalMatchedJobs = results[0].metadata[0]?.total || 0;
    const jobs = results[0].data;

    res.status(200).json({
      success: true,
      message: "Matched jobs fetched successfully",
      totalMatchedJobs,
      page,
      totalPages: Math.ceil(totalMatchedJobs / limit),
      results: jobs,
    });
  } catch (error) {
    console.error("Error fetching matched jobs:", error);
    return res.status(500).json({ message: error.message, success: false });
  }
};

//* @Desc get work upon jobs
//* @Route GET /api/v1/recruiter/work-upon-jobs
//* @Access Private - Recruiter
const getWorkJobs = async (req, res) => {
  try {
    const user = req.user;
    if (!user) {
      return res.status(400).json({
        success: false,
        message: "User not found",
      });
    }

    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    const results = await recruiterProfile.aggregate([
      {
        $match: {
          "user.userId": user.userId,
        },
      },

      {
        $unwind: {
          path: "$jobsWorkingOn",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $project: {
          _id: "$jobsWorkingOn.jobId",
          status: "$jobsWorkingOn.status",
          isActive: "$jobsWorkingOn.isActive",
        },
      },
      {
        $match: {
          status: "assigned",
          isActive: true,
        },
      },
      {
        $lookup: {
          from: "jobs",
          localField: "_id",
          foreignField: "_id",
          as: "_id",
        },
      },
      {
        $unwind: {
          path: "$_id",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $replaceRoot: {
          newRoot: "$_id",
        },
      },
      {
        $lookup: {
          from: "bookmarks",
          let: {
            userId: new mongoose.Types.ObjectId(user._id),
            jobID: "$_id",
          },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$jobId", "$$jobID"] },
                    { $eq: ["$recruiterId", "$$userId"] },
                  ],
                },
              },
            },
          ],
          as: "bookmark",
        },
      },
      {
        $addFields: {
          isBookmark: { $gt: [{ $size: "$bookmark" }, 0] },
        },
      },
      {
        $lookup: {
          from: "users",
          let: {
            userId: "$accountManager._id",
          },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [{ $eq: ["$_id", "$$userId"] }],
                },
              },
            },

            {
              $project: {
                name: 1,
                email: 1,
                userId: 1,
                phone: 1,
              },
            },
          ],
          as: "accountManager",
        },
      },
      {
        $unwind: {
          path: "$accountManager",
          preserveNullAndEmptyArrays: false,
        },
      },
      {
        $lookup: {
          from: "candidatesubmissions",
          let: { jobID: "$_id" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $eq: ["$job._id", "$$jobID"],
                },
              },
            },
            {
              $facet: {
                candidate: [
                  {
                    $group: {
                      _id: "$status",
                      count: {
                        $sum: 1,
                      },
                    },
                  },
                  {
                    $project: {
                      status: "$_id",
                      count: "$count",
                      _id: false,
                    },
                  },
                ],
                candidateStatus: [
                  {
                    $match: {
                      "submittedBy.userID": user.userId,
                    },
                  },
                  {
                    $group: {
                      _id: "$status",
                      count: {
                        $sum: 1,
                      },
                    },
                  },
                  {
                    $project: {
                      status: "$_id",
                      count: "$count",
                      _id: false,
                    },
                  },
                ],
                recruiterCount: [
                  {
                    $group: {
                      _id: "$submittedBy._id",
                    },
                  },
                  {
                    $count: "count",
                  },
                ],
              },
            },
          ],
          as: "submission",
        },
      },
      {
        $unwind: {
          path: "$submission",
          preserveNullAndEmptyArrays: false,
        },
      },
      {
        $project: {
          bookmark: 0,
        },
      },
      {
        $sort: {
          createdAt: -1,
        },
      },
      {
        $facet: {
          metadata: [{ $count: "total" }],
          data: [{ $skip: skip }, { $limit: limit }],
        },
      },
    ]);

    const totalworkonJobs = results[0].metadata[0]?.total || 0;
    const jobs = results[0].data;

    res.status(200).json({
      success: true,
      message: "workon jobs fetched successfully",
      totalworkonJobs,
      page,
      totalPages: Math.ceil(totalworkonJobs / limit),
      results: jobs,
    });
  } catch (error) {
    console.error("Error selecting job:", error);
    return res.status(500).json({ message: error.message, success: false });
  }
};

//* @Desc get work on request jobs
//* @Route GET /api/v1/recruiter/work-on-request
//* @Access Private - Recruiter
const getWorkOnRequestJobs = async (req, res) => {
  try {
    const user = req.user;
    if (!user) {
      return res.status(400).json({
        success: false,
        message: "User not found",
      });
    }

    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    const results = await workOnRequest.aggregate([
      {
        $match: {
          "recruiter.userId": user.userId,
          status: "requestToWork",
        },
      },
      {
        $lookup: {
          from: "jobs",
          localField: "job._id",
          foreignField: "_id",
          as: "job",
        },
      },
      {
        $unwind: {
          path: "$job",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $replaceRoot: {
          newRoot: "$job",
        },
      },
      {
        $lookup: {
          from: "users",
          let: { userId: "$accountManager._id" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $eq: ["$_id", "$$userId"],
                },
              },
            },
            {
              $project: {
                name: 1,
                email: 1,
                phone: 1,
                userId: 1,
              },
            },
          ],
          as: "accountManager",
        },
      },
      {
        $unwind: {
          path: "$accountManager",
          preserveNullAndEmptyArrays: true,
        },
      },

      {
        $lookup: {
          from: "bookmarks",
          let: {
            userId: new mongoose.Types.ObjectId(user._id),
            jobID: "$_id",
          },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$jobId", "$$jobID"] },
                    {
                      $eq: ["$recruiterId", "$$userId"],
                    },
                  ],
                },
              },
            },
          ],
          as: "bookmark",
        },
      },
      {
        $addFields: {
          isBookmark: {
            $gt: [{ $size: "$bookmark" }, 0],
          },
        },
      },
      {
        $lookup: {
          from: "candidatesubmissions",
          let: { jobID: "$_id" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $eq: ["$job._id", "$$jobID"],
                },
              },
            },
            {
              $facet: {
                candidate: [
                  {
                    $group: {
                      _id: "$status",
                      count: {
                        $sum: 1,
                      },
                    },
                  },
                  {
                    $project: {
                      status: "$_id",
                      count: "$count",
                      _id: false,
                    },
                  },
                ],

                recruiterCount: [
                  {
                    $group: {
                      _id: "$submittedBy._id",
                    },
                  },
                  {
                    $count: "count",
                  },
                ],
              },
            },
          ],
          as: "submission",
        },
      },
      {
        $unwind: {
          path: "$submission",
          preserveNullAndEmptyArrays: false,
        },
      },
      {
        $project: {
          bookmark: 0,
        },
      },
      {
        $sort: {
          createdAt: -1,
        },
      },
      {
        $facet: {
          metadata: [{ $count: "total" }],
          data: [{ $skip: skip }, { $limit: limit }],
        },
      },
    ]);

    const totalworkonrequestJobs = results[0].metadata[0]?.total || 0;
    const jobs = results[0].data;

    res.status(200).json({
      success: true,
      message: "workonrequest jobs fetched successfully",
      totalworkonrequestJobs,
      page,
      totalPages: Math.ceil(totalworkonrequestJobs / limit),
      results: jobs,
    });
  } catch (error) {
    console.error("Error selecting job:", error);
    return res.status(500).json({ message: error.message, success: false });
  }
};

//* @Desc get all jobs which is not workingon and not on workon request by manages
//* @Route GET /api/v1/recruiter/get-all-jobs
//* @Access Private - Recruiter
const getAllJobs = async (req, res) => {
  try {
    const user = req.user;
    if (!user) {
      return res.status(400).json({
        success: false,
        message: "User not found",
      });
    }

    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    const results = await job.aggregate([
      {
        $match: {
          isDeleted: false,
          visibility: true,
          accountManager: { $exists: true },
        },
      },
      {
        $lookup: {
          from: "workrequests",
          let: {
            jobId: "$_id",
            recruiterId: user.userId,
          },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$job._id", "$$jobId"] },
                    {
                      $eq: ["$recruiter.userId", "$$recruiterId"],
                    },
                    {
                      $in: ["$status", ["requestToWork", "accepted"]],
                    },
                  ],
                },
              },
            },
          ],
          as: "workrequest",
        },
      },
      {
        $lookup: {
          from: "recruiters",
          let: {
            jobId: "$_id",
            recruiterId: user.userId,
          },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    {
                      $eq: ["$user.userId", "$$recruiterId"],
                    },
                  ],
                },
              },
            },
            {
              $unwind: {
                path: "$jobsWorkingOn",
                preserveNullAndEmptyArrays: true,
              },
            },
            {
              $match: {
                $expr: {
                  $and: [
                    {
                      $eq: ["$jobsWorkingOn.jobId", "$$jobId"],
                    },
                    {
                      $eq: ["$jobsWorkingOn.isActive", true],
                    },
                    {
                      $eq: ["$jobsWorkingOn.status", "assigned"],
                    },
                  ],
                },
              },
            },
          ],
          as: "recruiter",
        },
      },
      {
        $addFields: {
          recruitercount: { $size: "$recruiter" },
          workrequestcount: { $size: "$workrequest" },
        },
      },
      {
        $match: {
          $expr: {
            $and: [
              { $eq: ["$recruitercount", 0] },
              { $eq: ["$workrequestcount", 0] },
            ],
          },
        },
      },

      {
        $lookup: {
          from: "users",
          let: { userId: "$accountManager._id" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $eq: ["$_id", "$$userId"],
                },
              },
            },
            {
              $project: {
                name: 1,
                email: 1,
                phone: 1,
                userId: 1,
              },
            },
          ],
          as: "accountManager",
        },
      },
      {
        $unwind: {
          path: "$accountManager",
          preserveNullAndEmptyArrays: true,
        },
      },

      {
        $lookup: {
          from: "bookmarks",
          let: {
            userId: new mongoose.Types.ObjectId(user._id),
            jobID: "$_id",
          },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$jobId", "$$jobID"] },
                    {
                      $eq: ["$recruiterId", "$$userId"],
                    },
                  ],
                },
              },
            },
          ],
          as: "bookmark",
        },
      },
      {
        $addFields: {
          isBookmark: {
            $gt: [{ $size: "$bookmark" }, 0],
          },
        },
      },
      {
        $lookup: {
          from: "candidatesubmissions",
          let: { jobID: "$_id" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $eq: ["$job._id", "$$jobID"],
                },
              },
            },
            {
              $facet: {
                candidate: [
                  {
                    $group: {
                      _id: "$status",
                      count: {
                        $sum: 1,
                      },
                    },
                  },
                  {
                    $project: {
                      status: "$_id",
                      count: "$count",
                      _id: false,
                    },
                  },
                ],

                recruiterCount: [
                  {
                    $group: {
                      _id: "$submittedBy._id",
                    },
                  },
                  {
                    $count: "count",
                  },
                ],
              },
            },
          ],
          as: "submission",
        },
      },
      {
        $unwind: {
          path: "$submission",
          preserveNullAndEmptyArrays: false,
        },
      },
      {
        $project: {
          recruitercount: 0,
          workrequestcount: 0,
          recruiter: 0,
          workrequest: 0,
          bookmark: 0,
        },
      },
      {
        $sort: {
          createdAt: -1,
        },
      },
      {
        $facet: {
          metadata: [{ $count: "total" }],
          data: [{ $skip: skip }, { $limit: limit }],
        },
      },
    ]);

    const totalallJobs = results[0].metadata[0]?.total || 0;
    const jobs = results[0].data;

    res.status(200).json({
      success: true,
      message: "all jobs fetched successfully",
      totalallJobs,
      page,
      totalPages: Math.ceil(totalallJobs / limit),
      results: jobs,
    });
  } catch (error) {
    console.error("Error selecting job:", error);
    return res.status(500).json({ message: error.message, success: false });
  }
};

//* @Desc get all save jobs
//* @Route GET /api/v1/recruiter/get-all-save-jobs
//* @Access Private - Recruiter
const getAllSaveJobs = async (req, res) => {
  try {
    const user = req.user;
    if (!user) {
      return res.status(400).json({
        success: false,
        message: "User not found",
      });
    }

    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    const results = await bookMark.aggregate([
      {
        $match: {
          recruiterId: new mongoose.Types.ObjectId(user._id),
        },
      },
      {
        $lookup: {
          from: "jobs",
          localField: "jobId",
          foreignField: "_id",
          as: "jobs",
        },
      },
      {
        $unwind: "$jobs",
      },
      {
        $replaceRoot: {
          newRoot: "$jobs",
        },
      },
      {
        $lookup: {
          from: "users",
          let: { userId: "$accountManager._id" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $eq: ["$_id", "$$userId"],
                },
              },
            },
            {
              $project: {
                name: 1,
                email: 1,
                phone: 1,
                userId: 1,
              },
            },
          ],
          as: "accountManager",
        },
      },
      {
        $unwind: {
          path: "$accountManager",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: "workrequests",
          let: {
            jobId: "$_id",
            recruiterId: new mongoose.Types.ObjectId(user._id),
          },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$job._id", "$$jobId"] },
                    {
                      $eq: ["$recruiter._id", "$$recruiterId"],
                    },
                    {
                      $in: ["$status", ["requestToWork"]],
                    },
                  ],
                },
              },
            },
          ],
          as: "workonrequest",
        },
      },
      {
        $lookup: {
          from: "recruiters",
          let: {
            jobId: "$_id",
            recruiterId: new mongoose.Types.ObjectId(user._id),
          },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    {
                      $eq: ["$user._id", "$$recruiterId"],
                    },
                  ],
                },
              },
            },
            {
              $unwind: {
                path: "$jobsWorkingOn",
                preserveNullAndEmptyArrays: true,
              },
            },
            {
              $match: {
                $expr: {
                  $and: [
                    {
                      $eq: ["$jobsWorkingOn.jobId", "$$jobId"],
                    },
                    {
                      $eq: ["$jobsWorkingOn.isActive", true],
                    },
                    {
                      $eq: ["$jobsWorkingOn.status", "assigned"],
                    },
                  ],
                },
              },
            },
          ],
          as: "workingon",
        },
      },
      {
        $addFields: {
          isBookmark: true,
          jobType: {
            $cond: [
              { $gt: [{ $size: "$workingon" }, 0] },
              "workingon",
              {
                $cond: [
                  {
                    $gt: [{ $size: "$workonrequest" }, 0],
                  },
                  "workonrequest",
                  "$$REMOVE",
                ],
              },
            ],
          },
        },
      },
      {
        $lookup: {
          from: "candidatesubmissions",
          let: { jobID: "$_id" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $eq: ["$job._id", "$$jobID"],
                },
              },
            },
            {
              $facet: {
                candidate: [
                  {
                    $group: {
                      _id: "$status",
                      count: {
                        $sum: 1,
                      },
                    },
                  },
                  {
                    $project: {
                      status: "$_id",
                      count: "$count",
                      _id: false,
                    },
                  },
                ],
                candidateStatus: [
                  {
                    $match: {
                      "submittedBy.userID": user.userId,
                    },
                  },
                  {
                    $group: {
                      _id: "$status",
                      count: {
                        $sum: 1,
                      },
                    },
                  },
                  {
                    $project: {
                      status: "$_id",
                      count: "$count",
                      _id: false,
                    },
                  },
                ],
                recruiterCount: [
                  {
                    $group: {
                      _id: "$submittedBy._id",
                    },
                  },
                  {
                    $count: "count",
                  },
                ],
              },
            },
          ],
          as: "submission",
        },
      },
      {
        $unwind: {
          path: "$submission",
          preserveNullAndEmptyArrays: false,
        },
      },
      {
        $project: {
          workonrequest: 0,
          workingon: 0,
        },
      },
      {
        $facet: {
          metadata: [{ $count: "total" }],
          data: [{ $skip: skip }, { $limit: limit }],
        },
      },
    ]);

    const totalallJobs = results[0].metadata[0]?.total || 0;
    const jobs = results[0].data;

    res.status(200).json({
      success: true,
      message: "all save jobs fetched successfully",
      totalallJobs,
      page,
      totalPages: Math.ceil(totalallJobs / limit),
      results: jobs,
    });
  } catch (error) {
    console.error("Error save job:", error);
    return res.status(500).json({ message: error.message, success: false });
  }
};

//* @Desc add to bookmark
//* @Route POST /api/v1/recruiter/add-to-bookmark
//* @Access Private - Recruiter
const addToBookMark = async (req, res) => {
  try {
    const user = req.user;
    if (!user) {
      return res.status(400).json({
        success: false,
        message: "User not found",
      });
    }

    const { jobId } = req.body;

    if (!jobId) {
      return res.status(400).json({
        success: false,
        message: "job not found",
      });
    }
    // check job exist
    const jobData = await job.findById(jobId);
    if (!jobData) {
      return res.status(400).json({
        success: false,
        message: "job not found",
      });
    }

    //is bookmark available
    const isJobBookmark = await bookMark.findOne({
      recruiterId: new mongoose.Types.ObjectId(user._id),
      jobId: new mongoose.Types.ObjectId(jobId),
    });

    if (isJobBookmark) {
      return res.status(400).json({
        success: false,
        message: "This job already been bookmarked.",
      });
    }

    // add to bookmark
    const isBookMark = new bookMark({
      jobId: jobId,
      recruiterId: user._id,
    });

    await isBookMark.save();

    res.status(200).json({
      success: true,
      message: "add bookmark successfully",
    });
  } catch (error) {
    console.error("Error add bookmark job:", error);
    return res.status(500).json({ message: error.message, success: false });
  }
};

//* @Desc add to bookmark
//* @Route DELETE /api/v1/recruiter/remove-to-bookmark
//* @Access Private - Recruiter
const removeFromBookMark = async (req, res) => {
  try {
    const user = req.user;
    if (!user) {
      return res.status(400).json({
        success: false,
        message: "User not found",
      });
    }

    const { jobId } = req.body;

    if (!jobId) {
      return res.status(400).json({
        success: false,
        message: "job not found",
      });
    }

    //is bookmark available
    const isJobBookmark = await bookMark.findOne({
      recruiterId: new mongoose.Types.ObjectId(user._id),
      jobId: new mongoose.Types.ObjectId(jobId),
    });

    if (!isJobBookmark) {
      return res.status(400).json({
        success: false,
        message: "This job not in bookmarked.",
      });
    }

    // find and update
    await bookMark.findOneAndDelete({
      recruiterId: new mongoose.Types.ObjectId(user._id),
      jobId: new mongoose.Types.ObjectId(jobId),
    });

    res.status(200).json({
      success: true,
      message: "remove bookmark successfully",
    });
  } catch (error) {
    console.error("Error removing bookmark job:", error);
    return res.status(500).json({ message: error.message, success: false });
  }
};

//* @Desc Select Job to work on
//* @Route POST /api/v1/recruiter/select-job-to-work-upon
//* @Access Private - Recruiter
const selectJobToWorkUpon = async (req, res) => {
  try {
    const user = req.user;
    if (!user) {
      return res.status(400).json({
        success: false,
        message: "User not found",
      });
    }
    const recruiterId = user?._id;

    const { jobId, workOnType } = req.body;

    if (!["workOn"].includes(workOnType)) {
      return res.status(400).json({
        success: false,
        message: "workonType are required.",
      });
    }

    const amountDeducted = workOnType == "instantSubmit" ? 2 : 1;

    if (!jobId || !recruiterId) {
      return res.status(400).json({
        success: false,
        message: "Job ID and Recruiter ID are required",
      });
    }

    const job = await Job.findById(jobId);
    if (!job) {
      return res.status(400).json({
        success: false,
        message: "Job not found",
      });
    }

    // check the user not work more then 10 job at one time
    const checkWorkon = await recruiterProfile.aggregate([
      {
        $match: {
          "user._id": new mongoose.Types.ObjectId(recruiterId),
        },
      },
      {
        $facet: {
          count: [
            {
              $unwind: {
                path: "$jobsWorkingOn",
                preserveNullAndEmptyArrays: true,
              },
            },
            {
              $match: {
                "jobsWorkingOn.isActive": true,
                "jobsWorkingOn.status": "assigned",
                $or: [
                  {
                    "jobsWorkingOn.isSlotEmpty": false,
                  },
                  {
                    "jobsWorkingOn.isSlotEmpty": {
                      $exists: false,
                    },
                  },
                ],
              },
            },
            { $count: "count" },
          ],
          data: [],
        },
      },
    ]);

    if (checkWorkon[0]?.count[0]?.count >= 10) {
      return res.status(400).json({
        success: false,
        message:
          "The maximum number of slots to work on this job is 10. You cannot assign more job beyond this limit.",
      });
    }
    const recruiter = await recruiterProfile.findOne({
      "user._id": new mongoose.Types.ObjectId(recruiterId),
    });

    if (!recruiter) {
      return res.status(400).json({
        success: false,
        message: "Recruiter not found",
      });
    }

    // Check if the job is already selected by the recruiter
    if (
      recruiter?.jobsWorkingOn?.filter(
        (item) =>
          item.jobId.toString() == jobId?.toString() &&
          item.status == "assigned" &&
          item.isActive
      ).length > 0
    ) {
      return res.status(400).json({
        success: false,
        message: "Job is already selected by the recruiter",
      });
    }

    const userCoinBalances = await userCoinBalance.findOne({
      userId: recruiter.user._id,
    });

    if (!userCoinBalances) {
      return res.status(400).json({
        success: false,
        message: "User coin balance not found",
      });
    }

    await CoinTransaction.create({
      userId: recruiter.user._id,
      relatedJobId: job._id,
      transactionType: "spent",
      quantity: 1, // Assuming a fixed amount for job selection
      balanceBefore: userCoinBalances.currentBalance,
      balanceAfter: userCoinBalances.currentBalance - amountDeducted,
      status: "completed",
      spendType: workOnType,
      amount: amountDeducted,
      createdAt: Date.now(),
    });

    // Update the recruiter to include the selected job
    recruiter.jobsWorkingOn.push({
      jobId,
      assignedBy: "self",
      assignedAt: Date.now(),
    });
    await recruiter.save();

    // Update the coin balance
    userCoinBalances.currentBalance -= amountDeducted; // Deduct 1 coin for job selection
    userCoinBalances.totalSpent += amountDeducted; // Update total spent
    userCoinBalances.lastTransactionAt = Date.now();
    await userCoinBalances.save();

    res.status(200).json({
      success: true,
      message: "Job selected successfully",
    });
  } catch (error) {
    console.error("Error selecting job:", error);
    return res.status(500).json({ message: error.message, success: false });
  }
};

//* @Desc work on request accept & reject
//* @Route POST /api/v1/recruiter/work-on-request-status-update
//* @Access Private - Recruiter
const updateWorkOnRequest = async (req, res) => {
  try {
    const user = req.user;
    if (!user) {
      return res.status(400).json({
        success: false,
        message: "User not found",
      });
    }

    const { jobId, status } = req.body;

    if (!(jobId && status && ["accepted", "rejected"].includes(status))) {
      return res.status(400).json({
        success: false,
        message: "jobid and status not found.",
      });
    }

    // check recruiter exist
    const recruiter = await recruiterProfile.findOne({
      "user._id": user?._id,
    });
    if (!recruiter) {
      return res.status(404).json({
        success: false,
        message: "Recruiter not found",
      });
    }

    // Check if the job is already selected by the recruiter
    if (
      recruiter?.jobsWorkingOn?.filter(
        (item) =>
          item.jobId.toString() == jobId?.toString() &&
          item.status == "assigned" &&
          item.isActive
      ).length > 0
    ) {
      return res.status(400).json({
        success: false,
        message: "Job is already selected by the recruiter",
      });
    }

    // update work on request
    await workOnRequest.findOneAndUpdate(
      {
        "job._id": new mongoose.Types.ObjectId(jobId),
        "recruiter.userId": user.userId,
      },
      { status: status }
    );

    if (status === "rejected") {
      // Update the recruiter to include the selected job for accept jobs
      recruiter.jobsWorkingOn.push({
        jobId,
        status: "removed",
        isActive: true,
      });
      await recruiter.save();
    } else {
      // Update the recruiter to include the selected job for accept jobs
      recruiter.jobsWorkingOn.push({
        jobId,
        assignedBy: "manager",
        assignedAt: Date.now(),
      });
      await recruiter.save();
    }

    res.status(200).json({
      success: true,
      message: "work on request update successfully",
    });
  } catch (error) {
    console.error("Error work on request update job:", error);
    return res.status(500).json({ message: error.message, success: false });
  }
};

//* @Desc dashboard stats
//* @Route POST /api/v1/recruiter/dashboard-stats
//* @Access Private - Recruiter
const dashboardStats = async (req, res) => {
  try {
    const user = req.user;
    if (!user) {
      return res.status(400).json({
        success: false,
        message: "User not found",
      });
    }

    const recruiter = await recruiterProfile.aggregate([
      {
        $match: {
          "user.userId": user.userId,
        },
      },
      {
        $lookup: {
          from: "jobs",
          localField: "jobsWorkingOn.jobId",
          foreignField: "_id",
          as: "jobDetails",
        },
      },
      {
        $addFields: {
          enrichedJobs: {
            $map: {
              input: "$jobsWorkingOn",
              as: "job",
              in: {
                $mergeObjects: [
                  "$$job",
                  {
                    jobInfo: {
                      $arrayElemAt: [
                        {
                          $filter: {
                            input: "$jobDetails",
                            as: "jobDetail",
                            cond: {
                              $eq: ["$$jobDetail._id", "$$job.jobId"],
                            },
                          },
                        },
                        0,
                      ],
                    },
                  },
                ],
              },
            },
          },
        },
      },
      {
        $project: {
          domain: 1,
          candidateRole: 1,
          workingOn: {
            $size: {
              $filter: {
                input: "$jobsWorkingOn",
                as: "job",
                cond: {
                  $and: [
                    {
                      $eq: ["$$job.status", "assigned"],
                    },
                    { $eq: ["$$job.isActive", true] },
                  ],
                },
              },
            },
          },
          recentAssignedJobs: {
            $filter: {
              input: "$enrichedJobs",
              as: "job",
              cond: {
                $and: [
                  { $eq: ["$$job.status", "assigned"] },
                  { $eq: ["$$job.isActive", true] },
                  {
                    $gte: [
                      "$$job.assignedAt",
                      {
                        $dateSubtract: {
                          startDate: "$$NOW",
                          unit: "hour",
                          amount: 24,
                        },
                      },
                    ],
                  },
                ],
              },
            },
          },
        },
      },
    ]);

    const job = await Job.aggregate([
      {
        $facet: {
          matchJob: [
            {
              $match: {
                jobProfile: {
                  $in: recruiter[0]?.candidateRole,
                },
                industry: { $in: recruiter[0]?.domain },
              },
            },
            {
              $lookup: {
                from: "workrequests",
                let: {
                  jobId: "$_id",
                  recruiterId: user.userId,
                },
                pipeline: [
                  {
                    $match: {
                      $expr: {
                        $and: [
                          { $eq: ["$job._id", "$$jobId"] },
                          {
                            $eq: ["$recruiter.userId", "$$recruiterId"],
                          },
                          {
                            $in: ["$status", ["requestToWork", "accepted"]],
                          },
                        ],
                      },
                    },
                  },
                ],
                as: "workrequest",
              },
            },
            {
              $lookup: {
                from: "recruiters",
                let: {
                  jobId: "$_id",
                  recruiterId: user.userId,
                },
                pipeline: [
                  {
                    $match: {
                      $expr: {
                        $eq: ["$user.userId", "$$recruiterId"],
                      },
                    },
                  },
                  {
                    $project: {
                      jobs: {
                        $filter: {
                          input: "$jobsWorkingOn",
                          as: "job",
                          cond: {
                            $and: [
                              {
                                $eq: ["$$job.jobId", "$$jobId"],
                              },
                              {
                                $eq: ["$$job.status", "assigned"],
                              },
                              {
                                $eq: ["$$job.isActive", true],
                              },
                            ],
                          },
                        },
                      },
                    },
                  },
                  {
                    $match: {
                      $expr: {
                        $gt: [{ $size: "$jobs" }, 0],
                      },
                    },
                  },
                ],
                as: "recruiter",
              },
            },
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: [{ $size: "$workrequest" }, 0] },
                    { $eq: [{ $size: "$recruiter" }, 0] },
                  ],
                },
              },
            },
            {
              $count: "total",
            },
          ],
          alljob: [
            {
              $match: {
                isDeleted: false,
                visibility: true,
                accountManager: { $exists: true },
              },
            },
            {
              $lookup: {
                from: "workrequests",
                let: { jobId: "$_id", recruiterId: user.userId },
                pipeline: [
                  {
                    $match: {
                      $expr: {
                        $and: [
                          { $eq: ["$job._id", "$$jobId"] },
                          { $eq: ["$recruiter.userId", "$$recruiterId"] },
                          { $in: ["$status", ["requestToWork", "accepted"]] },
                        ],
                      },
                    },
                  },
                ],
                as: "workrequest",
              },
            },
            {
              $lookup: {
                from: "recruiters",
                let: { jobId: "$_id", recruiterId: user.userId },
                pipeline: [
                  {
                    $match: {
                      $expr: { $eq: ["$user.userId", "$$recruiterId"] },
                    },
                  },
                  {
                    $project: {
                      jobsWorkingOn: {
                        $filter: {
                          input: "$jobsWorkingOn",
                          as: "job",
                          cond: {
                            $and: [
                              { $eq: ["$$job.jobId", "$$jobId"] },
                              { $eq: ["$$job.status", "assigned"] },
                              { $eq: ["$$job.isActive", true] },
                            ],
                          },
                        },
                      },
                    },
                  },
                  {
                    $match: {
                      $expr: { $gt: [{ $size: "$jobsWorkingOn" }, 0] },
                    },
                  },
                ],
                as: "recruiter",
              },
            },
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: [{ $size: "$workrequest" }, 0] },
                    { $eq: [{ $size: "$recruiter" }, 0] },
                  ],
                },
              },
            },
            {
              $count: "total",
            },
          ],
        },
      },
    ]);

    const workonrequest = await workOnRequest.aggregate([
      {
        $match: {
          "recruiter.userId": user.userId,
          status: "requestToWork",
        },
      },
      {
        $count: "requestToWork",
      },
    ]);

    const submission = await submissionLog.aggregate([
      {
        $lookup: {
          from: "candidatesubmissions",
          localField: "submissionID",
          foreignField: "_id",
          as: "submissionID",
        },
      },
      {
        $unwind: {
          path: "$submissionID",
          preserveNullAndEmptyArrays: false,
        },
      },

      {
        $match: {
          "submissionID.submittedBy.userID": user.userId,
        },
      },
      {
        $facet: {
          metadata: [{ $count: "total" }],
          data: [
            {
              $group: {
                _id: "$status",
                count: {
                  $sum: 1,
                },
              },
            },
          ],
        },
      },
    ]);

    const jobUpdate = await auditLog.aggregate([
      {
        $match: {
          visibleTo: "external",
          createdAt: {
            $gte: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
          },
        },
      },
      {
        $sort: {
          createdAt: -1,
        },
      },
      {
        $lookup: {
          from: "jobs",
          localField: "JobId",
          foreignField: "_id",
          as: "JobId",
        },
      },
      {
        $unwind: {
          path: "$JobId",
          preserveNullAndEmptyArrays: false,
        },
      },
      {
        $lookup: {
          from: "recruiters",
          let: {
            id: "$JobId._id",
            userId: user.userId,
          },
          pipeline: [
            {
              $match: {
                $expr: {
                  $eq: ["$user.userId", "$$userId"],
                },
              },
            },
            {
              $unwind: {
                path: "$jobsWorkingOn",
                preserveNullAndEmptyArrays: true,
              },
            },

            {
              $match: {
                "jobsWorkingOn.isActive": true,
                "jobsWorkingOn.status": "assigned",
                "jobsWorkingOn.assignedBy": "self",
              },
            },
            {
              $match: {
                $expr: {
                  $eq: ["$jobsWorkingOn.jobId", "$$id"],
                },
              },
            },
          ],
          as: "recruiters",
        },
      },
      {
        $addFields: {
          isRecruiter: { $size: "$recruiters" },
        },
      },
      {
        $match: {
          $expr: {
            $gt: ["$isRecruiter", 0],
          },
        },
      },
      {
        $project: {
          createdAt: "$createdAt",
          title: "$JobId.jobTitle",
          jobID: "$JobId.jobId",
          update: "$updateFields",
        },
      },
    ]);

    return res.status(200).json({
      success: true,
      message: "dashboard stats find",
      data: {
        workingOn: recruiter[0]?.workingOn,
        matchJob: job[0]?.matchJob[0]?.total,
        alljob: job[0]?.alljob[0]?.total,
        workonrequest: workonrequest[0]?.requestToWork,
        recentworkingon: recruiter[0]?.recentAssignedJobs,
        ...submission[0]?.data?.reduce((acc, item) => {
          acc[item._id] = item.count;
          return acc;
        }, {}),
        totalCandidiate: submission[0]?.metadata[0]?.total,
        jobUpdate,
      },
    });
  } catch (error) {
    return res.status(500).json({ message: error.message, success: false });
  }
};

module.exports = {
  unMappedJob,
  getAllRecruiters,
  getRecruiter,
  getMatchedJobs,
  selectJobToWorkUpon,
  getWorkJobs,
  getWorkOnRequestJobs,
  getAllJobs,
  addToBookMark,
  removeFromBookMark,
  getAllSaveJobs,
  updateWorkOnRequest,
  dashboardStats,
};
