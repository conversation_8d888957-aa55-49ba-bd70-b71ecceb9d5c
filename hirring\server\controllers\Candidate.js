const mongoose = require("mongoose");
const { Candidates } = require("../models/Candidate.models");
const { createID } = require("../utils");
const { uploadToS3 } = require("../configs/s3Config");
const job = require("../models/Job.models");
const {
  candidateSubmission,
  submissionEnums,
} = require("../models/CandidateSubmission.models");
const { workOnRequest } = require("../models/WorkOnRequest.models");
const recruiterProfile = require("../models/RecruiterProfile.models");
const CoinTransaction = require("../models/CoinTransaction");
const UserCoinBalance = require("../models/UserCoinBalance");
const { submissionLog } = require("../models/submissionLog.models");

//* @Desc get single condidate
//* @Route GET /api/v1/condidate/getcondidate/:candidateID
//* @Access private
const getCandidate = async (req, res) => {
  try {
    const user = req.user;
    if (!user) {
      return res.status(400).json({
        success: false,
        message: "User not found",
      });
    }
    const { candidateID } = req.params;
    const { submissionID } = req.query;

    if (!candidateID) {
      return res
        .status(400)
        .json({ success: false, message: "candidate ID is required." });
    }
    let query = {};
    if (user.userType === "recruiter") {
      query = {
        "createdBy.userID": user.userId,
        candidateID: candidateID,
      };
    }

    if (user.userType === "headAccountManager") {
      query = {
        candidateID: candidateID,
      };
    }

    if (user.userType === "accountManager") {
      query = {
        candidateID: candidateID,
      };
    }
    const submissions = await candidateSubmission.findOne({
      submissionId: submissionID,
    });

    const candidateData = await Candidates.find(query);
    res
      .status(200)
      .json({ success: true, data: { ...candidateData, submissions } });
  } catch (err) {
    console.error("Error in AddCandidate:", err);
    return res.status(500).json({
      success: false,
      message: "Something went wrong while fetching candidate",
      error: err.message,
    });
  }
};

//* @Desc get all condidate in basis of hr
//* @Route GET /api/v1/condidate/getallcandidate
//* @Access private
const getAllCandidate = async (req, res) => {
  try {
    const user = req.user;
    if (!user) {
      return res.status(400).json({
        success: false,
        message: "User not found",
      });
    }

    const condidateData = await Candidates.find({
      "createdBy.userID": user.userId,
      "isDeleted.flag": { $exists: false },
    });

    res.status(200).json({ success: true, data: condidateData });
  } catch (err) {
    console.error("Error in AddCandidate:", err);
    return res.status(500).json({
      success: false,
      message: "Something went wrong while fetching candidate",
      error: err.message,
    });
  }
};

//* @Desc create condidate
//* @Route POST /api/v1/condidate/addcondidate/personalDetails
//* @Access private
const AddCandidate = async (req, res) => {
  try {
    //User authentication check
    const user = req.user;
    if (!user) {
      return res.status(400).json({
        success: false,
        message: "User not found",
      });
    }

    const {
      firstName,
      lastName,
      phoneNumber,
      phoneCountryCode,
      emailAddress,
      currentAddress,
      country,
      city,
      state,
      zipcode,
      relocationWillingness = false,
      workAuthorizationStatus,
      ssnLast4Digit,
      availableStartDate,
    } = req.body;
    const errors = [];

    //field validation
    if (!firstName) errors.push("First name is required");
    if (!lastName) errors.push("Last name is required");
    if (!phoneNumber) errors.push("Valid 10-digit phone number is required");
    if (!phoneCountryCode) errors.push("country code is required");
    if (!emailAddress || !/\S+@\S+\.\S+/.test(emailAddress))
      errors.push("Valid email address is required");
    if (!currentAddress) errors.push("Current address is required");
    if (!country) errors.push("Country is required.");
    if (!city) errors.push("City is required");
    if (!state) errors.push("State is required");
    if (!zipcode) errors.push("Zipcode is required");
    if (!workAuthorizationStatus)
      errors.push("Work authorization status is required");
    if (ssnLast4Digit && !/^\d{4}$/.test(ssnLast4Digit))
      errors.push("SSN last 4 digits must be 4 numbers");

    //Return if validation failed
    if (errors.length > 0) {
      return res.status(400).json({
        success: false,
        message: "Validation failed",
        errors,
      });
    }

    const countCandidate = await Candidates.countDocuments();

    const ninetyDaysAgo = new Date(Date.now() - 90 * 24 * 60 * 60 * 1000);

    //Check for uniqueness (email and phone number)
    const existingCandidate = await Candidates.findOne({
      $and: [
        {
          $and: [
            { "personalDetails.emailAddress": emailAddress },
            { "personalDetails.phoneNumber": phoneNumber },
          ],
        },
        {
          "isDeleted.flag": { $ne: true },
        },
        {
          expireBy: { $gte: ninetyDaysAgo },
        },
      ],
    });

    if (existingCandidate) {
      return res.status(409).json({
        success: false,
        message: "Candidate with the same email or phone number already exists",
      });
    }

    //Create and save candidate
    const newCandidate = new Candidates({
      ...{
        candidateID: `HC${new Date().getFullYear()}${createID(
          countCandidate,
          1
        )}`,
        personalDetails: {
          firstName,
          lastName,
          phoneNumber,
          emailAddress,
          currentAddress,
          phoneCountryCode,
          city,
          country,
          state,
          zipcode,
          relocationWillingness,
          workAuthorizationStatus,
          ssnLast4Digit,
          availableStartDate,
        },
      },
      ...{ createdBy: { _id: user._id, userID: user.userId } },
    });
    const candidateRegister = await newCandidate.save();

    return res.status(201).json({
      success: true,
      message: "Candidate added successfully",
      data: candidateRegister,
    });
  } catch (err) {
    console.error("Error in AddCandidate:", err);
    return res.status(500).json({
      success: false,
      message: "Something went wrong while adding candidate",
      error: err.message,
    });
  }
};

//* @Desc add licensing
//* @Route PATCH /api/v1/condidate/addcondidate/licensing/:candidateID
//* @Access private
const Licensing = async (req, res) => {
  try {
    const { candidateID } = req.params;

    if (!candidateID) {
      return res
        .status(400)
        .json({ success: false, message: "candidate ID is required." });
    }

    const {
      stateLicense,
      stateLicenseExpiration,
      isCompactLicense = false,
    } = req.body;

    if (!(stateLicense && stateLicenseExpiration)) {
      return res
        .status(400)
        .json({ success: false, message: "All field is required." });
    }

    const updatedCandidate = await Candidates.findOneAndUpdate(
      { candidateID: candidateID },
      {
        $set: {
          "licensing.stateLicenses": stateLicense,
          "licensing.licenseExpireDate": stateLicenseExpiration,
          "licensing.compactLicense": isCompactLicense,
        },
      },
      { new: true }
    );

    if (!updatedCandidate) {
      return res.status(400).json({
        success: false,
        message: "No candidate update with licensing.",
      });
    }

    return res.status(200).json({
      success: true,
      message: "Candidate licensing added successfully",
      data: updatedCandidate,
    });
  } catch (error) {
    return res.status(400).json({ success: false, message: error?.message });
  }
};

//* @Desc add Certification
//* @Route PATCH /api/v1/condidate/addcondidate/certification/:candidateID
//* @Access private
const Certification = async (req, res) => {
  try {
    const { candidateID } = req.params;

    if (!candidateID) {
      return res
        .status(400)
        .json({ success: false, message: "candidate ID is required." });
    }

    const {
      blsCertification,
      blsExpiration,
      aclsPalsNals,
      aclsPalsNalsExpiration,
      otherRelevantCertificate = "",
    } = req.body;

    // if (
    //   !(
    //     aclsPalsNalsExpiration ||  &&
    //     aclsPalsNals
    //   )
    // ) {
    //   return res
    //     .status(400)
    //     .json({ success: false, message: "All field is required." });
    // }

    const updatedCandidate = await Candidates.findOneAndUpdate(
      { candidateID: candidateID },
      {
        $set: {
          "certification.blsCertification": blsCertification,
          "certification.blsExpiration": blsExpiration,
          "certification.aclsPalsNals": aclsPalsNals,
          "certification.aclsPalsNalsExpiration": aclsPalsNalsExpiration,
          "certification.otherRelevantCertificate": otherRelevantCertificate,
        },
      },
      { new: true }
    );

    if (!updatedCandidate) {
      return res.status(400).json({
        success: false,
        message: "No candidate update with certification.",
      });
    }

    return res.status(200).json({
      success: true,
      message: "Candidate certification added successfully",
      data: updatedCandidate,
    });
  } catch (error) {
    return res.status(400).json({ success: false, message: error?.message });
  }
};

//* @Desc add education
//* @Route PATCH /api/v1/condidate/addcondidate/education/:candidateID
//* @Access private
const Education = async (req, res) => {
  try {
    const { candidateID } = req.params;

    if (!candidateID) {
      return res
        .status(400)
        .json({ success: false, message: "candidate ID is required." });
    }

    const education = req.body;

    if (education.length < 1) {
      return res
        .status(400)
        .json({ success: false, message: "All field is required." });
    }

    const isFieldValid = education.filter(
      (item) => !(item.degree && item.collegeName && item.graduationYear)
    );

    if (isFieldValid.length > 0) {
      return res
        .status(400)
        .json({ success: false, message: "All field is required." });
    }

    const candidate = await Candidates.findOne({ candidateID: candidateID });
    if (!candidate) {
      return res
        .status(404)
        .json({ success: false, message: "Candidate not found." });
    }

    const existingEducation = candidate.education || [];

    // Remove duplicates: assuming a duplicate means all 3 fields match
    const uniqueNewEducation = education.filter((newEntry) => {
      return !existingEducation.some(
        (existingEntry) =>
          existingEntry.degree === newEntry.degree &&
          existingEntry.collegeName === newEntry.collegeName &&
          existingEntry.graduationYear === newEntry.graduationYear
      );
    });

    // Merge existing and new entries
    const updatedEducation = [...existingEducation, ...uniqueNewEducation];

    const updatedCandidate = await Candidates.findOneAndUpdate(
      { candidateID: candidateID },
      { $set: { education: updatedEducation } },
      { new: true }
    );

    if (!updatedCandidate) {
      return res.status(400).json({
        success: false,
        message: "No candidate update with Education.",
      });
    }

    return res.status(200).json({
      success: true,
      message: "Candidate Education added successfully",
      data: updatedCandidate,
    });
  } catch (error) {
    return res.status(400).json({ success: false, message: error?.message });
  }
};

//* @Desc add Work History
//* @Route PATCH /api/v1/condidate/addcondidate/workhistory/:candidateID
//* @Access private
const WorkHistory = async (req, res) => {
  try {
    const { candidateID } = req.params;

    if (!candidateID) {
      return res
        .status(400)
        .json({ success: false, message: "candidate ID is required." });
    }

    const {
      mostRecentEmployer,
      positionTitle,
      employmentDate,
      reasonForLeaving,
      supervisorReferenceName,
      supervisorReferenceTitle,
      supervisorReferenceContact,
      professionalReferenceName,
      professionalReferenceName2,
      professionalReferenceContact1,
      professionalReferenceContact2,
      professionalReferenceContact3,
    } = req.body;

    // add validation
    if (
      !mostRecentEmployer ||
      !positionTitle ||
      !employmentDate ||
      !reasonForLeaving ||
      !supervisorReferenceName ||
      !supervisorReferenceTitle ||
      !supervisorReferenceContact ||
      !professionalReferenceName ||
      !professionalReferenceContact1
    ) {
      return res.status(400).json({
        success: false,
        message: "All required fields must be provided.",
      });
    }

    const updatedCandidate = await Candidates.findOneAndUpdate(
      { candidateID: candidateID },
      {
        workHistory: {
          mostRecentEmployer,
          positionTitle,
          employmentDate,
          reasonForLeaving,
          supervisorReferenceName,
          supervisorReferenceTitle,
          supervisorReferenceContact,
          professionalReferenceName,
          professionalReferenceName2,
          professionalReferenceContact1,
          professionalReferenceContact2,
          professionalReferenceContact3,
        },
      },
      { new: true }
    );

    if (!updatedCandidate) {
      return res.status(400).json({
        success: false,
        message: "No candidate update with Work History.",
      });
    }

    return res.status(200).json({
      success: true,
      message: "Candidate Work History added successfully",
      data: updatedCandidate,
    });
  } catch (error) {
    return res.status(400).json({ success: false, message: error?.message });
  }
};

//* @Desc add skills And Experience
//* @Route PATCH /api/v1/condidate/addcondidate/skillsandexperience/:candidateID
//* @Access private
const skillsAndExperience = async (req, res) => {
  try {
    const { candidateID } = req.params;

    if (!candidateID) {
      return res
        .status(400)
        .json({ success: false, message: "candidate ID is required." });
    }

    const { totalYearsOfExperience, relevantExperience, otherSkills } =
      req.body;

    if (!(totalYearsOfExperience && relevantExperience)) {
      return res
        .status(400)
        .json({ success: false, message: "All field is required." });
    }

    const updatedCandidate = await Candidates.findOneAndUpdate(
      { candidateID: candidateID },
      {
        $set: {
          "skillsAndExperience.totalYearsOfExperience": totalYearsOfExperience,
          "skillsAndExperience.relevantExperience": relevantExperience,
          "skillsAndExperience.otherSkills": otherSkills
            ? otherSkills
                ?.split(",")
                ?.filter((item) => item)
                ?.map((item) => item?.trim())
            : [],
        },
      },
      { new: true }
    );

    if (!updatedCandidate) {
      return res.status(400).json({
        success: false,
        message: "No candidate update with skills And Experience.",
      });
    }

    return res.status(200).json({
      success: true,
      message: "Candidate skills And Experience added successfully",
      data: updatedCandidate,
    });
  } catch (error) {
    return res.status(400).json({ success: false, message: error?.message });
  }
};

//* @Desc add Health And Compliance
//* @Route PATCH /api/v1/condidate/addcondidate/healthandcompliance/:candidateID
//* @Access private
const HealthAndCompliance = async (req, res) => {
  try {
    const { candidateID } = req.params;

    if (!candidateID) {
      return res
        .status(400)
        .json({ success: false, message: "candidate ID is required." });
    }

    const {
      covid19Status,
      dateOfLastCovid19Dose,
      boosterReceived,
      proofOfVaccinationAvailable,
      fluVaccination,
    } = req.body;

    if (
      !(
        covid19Status &&
        boosterReceived &&
        proofOfVaccinationAvailable &&
        fluVaccination
      )
    ) {
      return res
        .status(400)
        .json({ success: false, message: "All field is required." });
    }

    const updatedCandidate = await Candidates.findOneAndUpdate(
      { candidateID: candidateID },
      {
        $set: {
          "healthAndCompliance.covid19Status":
            covid19Status == "none" || covid19Status == "exempt" ? false : true,
          "healthAndCompliance.dateOfLastCovid19Dose": dateOfLastCovid19Dose,
          "healthAndCompliance.boosterReceived":
            boosterReceived == "yes" ? true : false,
          "healthAndCompliance.proofOfVaccinationAvailable":
            proofOfVaccinationAvailable == "yes" ? true : false,
          "healthAndCompliance.fluVaccination":
            fluVaccination == "yes" ? true : false,
        },
      },
      { new: true }
    );

    if (!updatedCandidate) {
      return res.status(400).json({
        success: false,
        message: "No candidate update with Health And Compliance.",
      });
    }

    return res.status(200).json({
      success: true,
      message: "Candidate Health And Compliance added successfully",
      data: updatedCandidate,
    });
  } catch (error) {
    return res.status(400).json({ success: false, message: error?.message });
  }
};

//* @Desc add Submission Details
//* @Route PATCH /api/v1/condidate/addcondidate/submissiondetails/:candidateID
//* @Access private
const SubmissionDetails = async (req, res) => {
  try {
    const { candidateID } = req.params;

    if (!candidateID) {
      return res
        .status(400)
        .json({ success: false, message: "candidate ID is required." });
    }

    const {
      rateExpectation,
      referenceProvider,
      candidateAvailabilityForInterview,
      additionalNote,
    } = req.body;

    if (
      !(
        rateExpectation &&
        ["Hourly", "Salary"].includes(rateExpectation) &&
        referenceProvider &&
        ["Yes", "No"].includes(referenceProvider) &&
        candidateAvailabilityForInterview
      )
    ) {
      return res
        .status(400)
        .json({ success: false, message: "All field is required." });
    }

    const updatedCandidate = await Candidates.findOneAndUpdate(
      { candidateID: candidateID },
      {
        $set: {
          "submissionDetails.rateExpectation": rateExpectation,
          "submissionDetails.referenceProvider": referenceProvider,
          "submissionDetails.candidateAvailabilityForInterview":
            candidateAvailabilityForInterview,
          "submissionDetails.additionalNote": additionalNote
            ? additionalNote
            : "",
        },
      },
      { new: true }
    );

    if (!updatedCandidate) {
      return res.status(400).json({
        success: false,
        message: "No candidate update with Submission Details.",
      });
    }

    return res.status(200).json({
      success: true,
      message: "Candidate Submission Details added successfully",
      data: updatedCandidate,
    });
  } catch (error) {
    return res.status(400).json({ success: false, message: error?.message });
  }
};

//* @Desc add Document Attachment
//* @Route PATCH /api/v1/condidate/addcondidate/documentattachment/:candidateID
//* @Access private
const DocumentAttachment = async (req, res) => {
  try {
    const { candidateID } = req.params;
    if (!candidateID) {
      return res
        .status(400)
        .json({ success: false, message: "candidate ID is required." });
    }

    const file = req.files;

    //Both resume and cover letter must be present — either in the database or in the uploaded files. If either one is missing from both, return an error.
    const candidate = await Candidates.findOne({ candidateID: candidateID });

    const hasResumeInDB = !!candidate?.documentAttachments?.resume;
    const hasCoverLetterInDB = !!candidate?.documentAttachments?.coverLetter;

    const hasResumeInForm = file?.some((f) => f.fieldname === "resume");
    const hasCoverLetterInForm = file?.some(
      (f) => f.fieldname === "coverLetter"
    );

    const hasResume = hasResumeInDB || hasResumeInForm;
    const hasCoverLetter = hasCoverLetterInDB || hasCoverLetterInForm;

    if (!hasResume && !hasCoverLetter) {
      return res.status(400).json({
        success: false,
        message: "Resume and cover letter are required.",
      });
    }

    if (!hasResume) {
      return res
        .status(400)
        .json({ success: false, message: "Resume is required." });
    }

    if (!hasCoverLetter) {
      return res
        .status(400)
        .json({ success: false, message: "Cover letter is required." });
    }

    // file upload to s3
    const s3Urls = {};
    try {
      async function proccess(index) {
        if (index == file.length) {
          return;
        }
        let fileData = file[index];

        let uploadUrl = await uploadToS3(
          fileData.buffer,
          fileData.originalname,
          fileData.mimetype,
          "candidate"
        );
        s3Urls[fileData.fieldname] = uploadUrl;
        await proccess(index + 1);
      }
      await proccess(0);
    } catch (error) {
      console.error("Error uploading file:", uploadError);
      return res.status(400).json({
        success: false,
        message: "Failed to upload candidate document",
      });
    }

    const updatedCandidate = await Candidates.findOneAndUpdate(
      { candidateID: candidateID },
      { $set: { documentAttachments: s3Urls } },
      { new: true }
    );

    if (!updatedCandidate) {
      return res.status(400).json({
        success: false,
        message: "No candidate update with Education.",
      });
    }

    return res.status(200).json({
      success: true,
      message: "Candidate Document upload successfully",
      data: updatedCandidate,
    });
  } catch (error) {
    return res.status(400).json({ success: false, message: error?.message });
  }
};

//* @Desc update condidate
//* @Route PATCH /api/v1/condidate/updatecandidate/personaldetails/:candidateID
//* @Access private
const updateCandidate = async (req, res) => {
  try {
    const user = req.user;
    if (!user) {
      return res.status(400).json({
        success: false,
        message: "User not found",
      });
    }

    const { candidateID } = req.params;
    if (!candidateID) {
      return res
        .status(400)
        .json({ success: false, message: "candidate ID is required." });
    }

    const {
      firstName,
      lastName,
      phoneNumber,
      emailAddress,
      currentAddress,
      city,
      state,
      country,
      phoneCountryCode,
      zipcode,
      relocationWillingness = false,
      workAuthorizationStatus,
      ssnLast4Digit,
      availableStartDate,
    } = req.body;
    const errors = [];

    //field validation
    if (!firstName) errors.push("First name is required");
    if (!lastName) errors.push("Last name is required");
    if (!phoneNumber) errors.push("Valid 10-digit phone number is required");

    if (!phoneCountryCode) errors.push("phone country code is required");
    if (!country) errors.push("Country is required.");

    if (!emailAddress || !/\S+@\S+\.\S+/.test(emailAddress))
      errors.push("Valid email address is required");
    if (!currentAddress) errors.push("Current address is required");
    if (!city) errors.push("City is required");
    if (!state) errors.push("State is required");
    if (!zipcode) errors.push("Zipcode is required");
    if (!workAuthorizationStatus)
      errors.push("Work authorization status is required");
    if (ssnLast4Digit && !/^\d{4}$/.test(ssnLast4Digit))
      errors.push("SSN last 4 digits must be 4 numbers");

    //Return if validation failed
    if (errors.length > 0) {
      return res.status(400).json({
        success: false,
        message: "Validation failed",
        errors,
      });
    }

    //Check for uniqueness (email and phone number)
    const existingCandidate = await Candidates.findOne({
      "createdBy.userID": user.userId,
      candidateID: candidateID,
    });

    if (!existingCandidate) {
      return res.status(400).json({
        success: false,
        message: "Recruiter with this candidate does not exist.",
      });
    }

    const updatedCandidate = await Candidates.findOneAndUpdate(
      { candidateID: candidateID },
      {
        $set: {
          "personalDetails.firstName": firstName,
          "personalDetails.lastName": lastName,
          "personalDetails.phoneNumber": phoneNumber,
          "personalDetails.emailAddress": emailAddress,
          "personalDetails.currentAddress": currentAddress,
          "personalDetails.phoneCountryCode": phoneCountryCode,
          "personalDetails.country": country,
          "personalDetails.city": city,
          "personalDetails.state": state,
          "personalDetails.zipcode": zipcode,
          "personalDetails.relocationWillingness": relocationWillingness,
          "personalDetails.workAuthorizationStatus": workAuthorizationStatus,
          "personalDetails.ssnLast4Digit": ssnLast4Digit,
          "personalDetails.availableStartDate": availableStartDate,
        },
      },
      { new: true }
    );

    if (!updatedCandidate) {
      return res.status(400).json({
        success: false,
        message: "No candidate update.",
      });
    }

    return res.status(200).json({
      success: true,
      message: "Candidate profile is update successfully",
      data: updatedCandidate,
    });
  } catch (err) {
    console.error("Error updating candidate:", err);
    return res.status(500).json({
      success: false,
      message: "Something went wrong while updating candidate",
      error: err.message,
    });
  }
};

//* @Desc remove single condidate
//* @Route DELETE /api/v1/condidate/removecondidate/:candidateID
//* @Access private
const removeCandidate = async (req, res) => {
  try {
    const user = req.user;
    if (!user) {
      return res.status(400).json({
        success: false,
        message: "User not found",
      });
    }

    const { candidateID } = req.params;
    if (!candidateID) {
      return res
        .status(400)
        .json({ success: false, message: "candidate ID is required." });
    }

    const candidateData = await Candidates.findOneAndUpdate(
      { candidateID: candidateID },
      {
        $set: {
          "isDeleted.flag": true,
          "isDeleted._id": user._id,
          "isDeleted.userId": user.userId,
        },
      },
      { new: true }
    );

    return res.status(200).json({
      success: true,
      message: "Candidate marked as deleted",
      data: candidateData,
    });
  } catch (err) {
    console.error("Error in removeCandidate:", err);
    return res.status(500).json({
      success: false,
      message: "Something went wrong while deleting candidate",
      error: err.message,
    });
  }
};

//* @Desc submit candidate for jobs
//* @Route POST /api/v1/condidate/candidateJobSubmissions
//* @Access private
const candidateJobSubmissions = async (req, res) => {
  try {
    const user = req.user;
    if (!user) {
      return res.status(400).json({
        success: false,
        message: "User not found",
      });
    }

    const { jobID, candidateID } = req.body;

    if (!(jobID && candidateID)) {
      return res.status(400).json({
        success: false,
        message: "All filed are requires",
      });
    }

    const jobDetails = await job.findOne({ jobId: jobID });
    if (!jobDetails) {
      return res.status(400).json({
        success: false,
        message: "job not found",
      });
    }

    const isWorkRequest = await recruiterProfile.findOne({
      "user.userId": user.userId,
      jobsWorkingOn: {
        $elemMatch: {
          jobId: new mongoose.Types.ObjectId(jobDetails?._id),
          isActive: true,
          status: "assigned",
        },
      },
    });

    if (!isWorkRequest) {
      return res.status(400).json({
        success: false,
        message:
          "You do not have permission to submit a candidate for this job. Please start working on the job first to gain access.",
      });
    }

    const workRequest = await candidateSubmission.findOne({
      "job.jobID": jobID,
      "candidate.candidateID": candidateID,
    });

    if (workRequest) {
      return res.status(400).json({
        success: false,
        message: "candidate is already submitted on this request.",
      });
    }

    const candidiateDetails = await Candidates.findOne({
      candidateID: candidateID,
    });
    if (!candidiateDetails) {
      return res.status(400).json({
        success: false,
        message: "candidate not found",
      });
    }

    const CandidateSubmissionIntance = new candidateSubmission({
      job: {
        _id: jobDetails._id,
        jobID: jobDetails.jobId,
      },
      submittedBy: {
        _id: user._id,
        userID: user.userId,
      },
      candidate: {
        _id: candidiateDetails._id,
        candidateID: candidiateDetails.candidateID,
      },
    });
    const newCandidateSubmission = await CandidateSubmissionIntance.save();

    let eventDate = new Date();
    eventDate.setHours(0, 0, 0, 0);

    // add logs that status will be updated
    const submissionEventLog = new submissionLog({
      status: "submitted",
      submittedBy: user._id,
      submissionID: newCandidateSubmission._id,
      eventDate: eventDate,
    });

    await submissionEventLog.save();
    return res.status(200).json({
      success: true,
      message: "candidate mapped with job",
      data: newCandidateSubmission,
    });
  } catch (error) {
    console.error("Error in candidate submission:", error);
    return res.status(500).json({
      success: false,
      message: "Something went wrong while candidate submission",
      error: error.message,
    });
  }
};

//* @Desc get candidate submissions with there status like hired,rejected,active & all submissions
//* @Route GET /api/v1/condidate/getSubmissions
//* @Access private, recruiter
const getSubmissions = async (req, res) => {
  try {
    const user = req.user;
    if (!user) {
      return res.status(400).json({
        success: false,
        message: "User not found",
      });
    }

    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    const {
      submissionType = "any",
      location: locationParam,
      status: statusParam,
      experienceLevel: experienceLevelParam,
      jobType: jobTypeParam,
      searchTerm,
      sortBy = "newest"
    } = req.query;

    // Parse comma-separated arrays back to arrays
    const location = locationParam ? locationParam.split(',').map(item => item.trim()) : undefined;
    const status = statusParam ? statusParam.split(',').map(item => item.trim()) : undefined;

    // Parse new filter parameters for hired candidates and all submissions
    const jobType = jobTypeParam ? jobTypeParam.split(',').map(item => item.trim()) : undefined;
    const experienceLevel = experienceLevelParam ? experienceLevelParam.split(',').map(item => item.trim()) : undefined;

    let query = {};
    let additionalMatchStages = [];
    let postLookupMatchStages = [];

    // Base status filtering by tab type
    if (submissionType == "activeSubmission") {
      query = {
        status: {
          $nin: [
            "rejected",
            "offer rejected",
            "guarantee period not completed",
            "guarantee period completed",
            "Talent Pool",
          ],
        },
      };
    }

    if (submissionType == "hired") {
      query = {
        status: {
          $in: ["offer accepted", "hired-under guarantee period"],
        },
      };
    }

    if (submissionType == "rejected") {
      query = {
        status: {
          $in: ["rejected", "offer rejected", "guarantee period not completed"],
        },
      };
    }

    // Enhanced filtering for Active Submissions tab
    if (submissionType == "activeSubmission") {
      // Location filtering
      if (location && location.length > 0) {
        const locations = Array.isArray(location) ? location : [location];
        // Create regex patterns for partial matching
        const locationRegexPatterns = locations.map(loc => new RegExp(loc, 'i'));

        additionalMatchStages.push({
          $match: {
            $or: [
              { "personalDetails.city": { $in: locationRegexPatterns } },
              { "personalDetails.state": { $in: locationRegexPatterns } },
              { "personalDetails.country": { $in: locationRegexPatterns } }
            ]
          }
        });
      }

      // Status filtering - Filter on submission status, not candidate status
      if (status && status.length > 0) {
        const statuses = Array.isArray(status) ? status : [status];
        // Use case-insensitive regex match for status
        postLookupMatchStages.push({
          $match: {
            $and: [
              { "submission": { $exists: true, $ne: null } },
              {
                $expr: {
                  $in: [
                    { $toLower: "$submission.status" },
                    statuses.map(s => s.toLowerCase())
                  ]
                }
              }
            ]
          }
        });
      }



      // Search functionality
      if (searchTerm && searchTerm.trim()) {
        const searchRegex = new RegExp(searchTerm.trim(), 'i');
        additionalMatchStages.push({
          $match: {
            $or: [
              { "personalDetails.firstName": searchRegex },
              { "personalDetails.lastName": searchRegex },
              { "personalDetails.emailAddress": searchRegex },
              { candidateID: searchRegex }
            ]
          }
        });
      }
    }

    // Enhanced filtering for Hired Candidates tab
    if (submissionType == "hired") {

      // Location filtering
      if (location && location.length > 0) {
        const locations = Array.isArray(location) ? location : [location];
        const locationRegexPatterns = locations.map(loc => new RegExp(loc, 'i'));

        additionalMatchStages.push({
          $match: {
            $or: [
              { "personalDetails.city": { $in: locationRegexPatterns } },
              { "personalDetails.state": { $in: locationRegexPatterns } },
              { "personalDetails.country": { $in: locationRegexPatterns } }
            ]
          }
        });
      }

      // Status filtering - Filter on submission status, not candidate status
      if (status && status.length > 0) {
        const statuses = Array.isArray(status) ? status : [status];
        // Use case-insensitive regex match for status
        postLookupMatchStages.push({
          $match: {
            $and: [
              { "submission": { $exists: true, $ne: null } },
              {
                $expr: {
                  $in: [
                    { $toLower: "$submission.status" },
                    statuses.map(s => s.toLowerCase())
                  ]
                }
              }
            ]
          }
        });
      }

      // Job Type filtering - Filter on job.jobType through submission relationship
      if (jobType && jobType.length > 0) {
        const jobTypes = Array.isArray(jobType) ? jobType : [jobType];
        // Normalize filter values: lowercase, remove spaces and hyphens
        const normalizedJobTypes = jobTypes.map(j => j.toLowerCase().replace(/\s|-/g, ""));
        postLookupMatchStages.push({
          $match: {
            $and: [
              { "submission": { $exists: true, $ne: null } },
              {
                $expr: {
                  $in: [
                    {
                      $replaceAll: {
                        input: {
                          $replaceAll: {
                            input: { $toLower: { $trim: { input: { $ifNull: ["$submission.job.jobType", ""] } } } },
                            find: " ",
                            replacement: ""
                          }
                        },
                        find: "-",
                        replacement: ""
                      }
                    },
                    normalizedJobTypes
                  ]
                }
              }
            ]
          }
        });
      }

      // Experience Level filtering (move to post-lookup if jobType is also selected)
      if (experienceLevel && experienceLevel.length > 0 && jobType && jobType.length > 0) {
        const expLevels = Array.isArray(experienceLevel) ? experienceLevel : [experienceLevel];
        const expConditions = [];
        expLevels.forEach(level => {
          const [minExp, maxExp] = level.split('-').map(Number);
          if (!isNaN(minExp) && !isNaN(maxExp)) {
            expConditions.push({
              $and: [
                {
                  $gte: [
                    {
                      $toInt: {
                        $arrayElemAt: [
                          { $split: ["$skillsAndExperience.totalYearsOfExperience", " "] },
                          0
                        ]
                      }
                    },
                    minExp
                  ]
                },
                {
                  $lte: [
                    {
                      $toInt: {
                        $arrayElemAt: [
                          { $split: ["$skillsAndExperience.totalYearsOfExperience", " "] },
                          0
                        ]
                      }
                    },
                    maxExp
                  ]
                }
              ]
            });
          }
        });
        if (expConditions.length > 0) {
          postLookupMatchStages.push({
            $match: {
              $expr: {
                $or: expConditions
              }
            }
          });
        }
      } else if (experienceLevel && experienceLevel.length > 0) {
        // If only experienceLevel is selected, keep the original logic
        const expLevels = Array.isArray(experienceLevel) ? experienceLevel : [experienceLevel];
        const expConditions = [];
        expLevels.forEach(level => {
          const [minExp, maxExp] = level.split('-').map(Number);
          if (!isNaN(minExp) && !isNaN(maxExp)) {
            expConditions.push({
              $and: [
                {
                  $gte: [
                    {
                      $toInt: {
                        $arrayElemAt: [
                          { $split: ["$skillsAndExperience.totalYearsOfExperience", " "] },
                          0
                        ]
                      }
                    },
                    minExp
                  ]
                },
                {
                  $lte: [
                    {
                      $toInt: {
                        $arrayElemAt: [
                          { $split: ["$skillsAndExperience.totalYearsOfExperience", " "] },
                          0
                        ]
                      }
                    },
                    maxExp
                  ]
                }
              ]
            });
          }
        });
        if (expConditions.length > 0) {
          additionalMatchStages.push({
            $match: {
              $expr: {
                $or: expConditions
              }
            }
          });
        }
      }

      // Search functionality
      if (searchTerm && searchTerm.trim()) {
        const searchRegex = new RegExp(searchTerm.trim(), 'i');
        additionalMatchStages.push({
          $match: {
            $or: [
              { "personalDetails.firstName": searchRegex },
              { "personalDetails.lastName": searchRegex },
              { "personalDetails.emailAddress": searchRegex },
              { candidateID: searchRegex }
            ]
          }
        });
      }
    }

    // Enhanced filtering for All Submissions tab
    if (submissionType == "any") {
      // Location filtering
      if (location && location.length > 0) {
        const locations = Array.isArray(location) ? location : [location];
        const locationRegexPatterns = locations.map(loc => new RegExp(loc, 'i'));

        additionalMatchStages.push({
          $match: {
            $or: [
              { "personalDetails.city": { $in: locationRegexPatterns } },
              { "personalDetails.state": { $in: locationRegexPatterns } },
              { "personalDetails.country": { $in: locationRegexPatterns } }
            ]
          }
        });
      }

      // Status filtering - Filter on submission status, not candidate status
      if (status && status.length > 0) {
        const statuses = Array.isArray(status) ? status : [status];
        // Add status filter to the submission lookup pipeline
        postLookupMatchStages.push({
          $match: {
            $and: [
              { "submission": { $exists: true, $ne: null } }, // Ensure submission exists
              {
                $expr: {
                  $in: [
                    { $toLower: "$submission.status" },
                    statuses.map(s => s.toLowerCase())
                  ]
                }
              }
            ]
          }
        });
      }

      // Job Type filtering - Filter on job.jobType through submission relationship
      if (jobType && jobType.length > 0) {
        const jobTypes = Array.isArray(jobType) ? jobType : [jobType];
        postLookupMatchStages.push({
          $match: {
            $and: [
              { "submission": { $exists: true, $ne: null } },
              {
                $expr: {
                  $in: [
                    { $toLower: { $trim: { input: { $ifNull: ["$submission.job.jobType", ""] } } } },
                    jobTypes.map(j => j.toLowerCase())
                  ]
                }
              }
            ]
          }
        });
      }

      // Experience Level filtering (for All Submissions tab)
      if (experienceLevel && experienceLevel.length > 0) {
        const expLevels = Array.isArray(experienceLevel) ? experienceLevel : [experienceLevel];
        const expConditions = [];

        expLevels.forEach(level => {
          const [minExp, maxExp] = level.split('-').map(Number);
          if (!isNaN(minExp) && !isNaN(maxExp)) {
            expConditions.push({
              $and: [
                {
                  $gte: [
                    {
                      $toInt: {
                        $arrayElemAt: [
                          { $split: ["$skillsAndExperience.totalYearsOfExperience", " "] },
                          0
                        ]
                      }
                    },
                    minExp
                  ]
                },
                {
                  $lte: [
                    {
                      $toInt: {
                        $arrayElemAt: [
                          { $split: ["$skillsAndExperience.totalYearsOfExperience", " "] },
                          0
                        ]
                      }
                    },
                    maxExp
                  ]
                }
              ]
            });
          }
        });

        if (expConditions.length > 0) {
          additionalMatchStages.push({
            $match: {
              $expr: {
                $or: expConditions
              }
            }
          });
        }
      }

      // Search functionality
      if (searchTerm && searchTerm.trim()) {
        const searchRegex = new RegExp(searchTerm.trim(), 'i');
        additionalMatchStages.push({
          $match: {
            $or: [
              { "personalDetails.firstName": searchRegex },
              { "personalDetails.lastName": searchRegex },
              { "personalDetails.emailAddress": searchRegex },
              { candidateID: searchRegex }
            ]
          }
        });
      }
    }

    // Build the aggregation pipeline
    let pipeline = [
      {
        $match: {
          "createdBy.userID": user.userId,
        },
      },
      // Add additional match stages for filtering (location, experience, search)
      ...additionalMatchStages,
      {
        $lookup: {
          from: "candidatesubmissions",
          let: {
            candidateID: "$candidateID",
            userID: "$createdBy.userID",
          },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    {
                      $eq: ["$candidate.candidateID", "$$candidateID"],
                    },
                    {
                      $eq: ["$submittedBy.userID", "$$userID"],
                    },
                  ],
                },
              },
            },
            {
              $lookup: {
                from: "jobs",
                localField: "job._id",
                foreignField: "_id",
                as: "job",
              },
            },
            {
              $unwind: {
                path: "$job",
                preserveNullAndEmptyArrays: false,
              },
            },
          ],
          as: "submission",
        },
      },
      // Unwind only if there are submissions, otherwise filter out nulls
      {
        $unwind: {
          path: "$submission",
          preserveNullAndEmptyArrays: false, // Only keep candidates with submissions
        },
      },
      {
        $addFields: {
          status: {
            $ifNull: ["$submission.status", "Talent Pool"],
          },
        },
      },
      {
        $match: query,
      },
      // Add post-lookup filtering stages (status, job type)
      ...postLookupMatchStages
    ];

    // Add sorting logic
    console.log("🔄 Sorting applied:", sortBy);
    console.log("🔄 All query params:", req.query);
    let sortStage = {};
    if (sortBy === "newest") {
      // Sort by submission date (newest first)
      sortStage = {
        $sort: {
          "submission.submittedAt": -1
        }
      };
    } else if (sortBy === "oldest") {
      // Sort by submission date (oldest first)
      sortStage = {
        $sort: {
          "submission.submittedAt": 1
        }
      };
    } else {
      // Default sorting (newest first)
      sortStage = {
        $sort: {
          "submission.submittedAt": -1
        }
      };
    }
    console.log("🔄 Sort stage:", JSON.stringify(sortStage, null, 2));

    // Add pagination with sorting
    pipeline.push({
      $facet: {
        metadata: [{ $count: "total" }],
        data: [sortStage, { $skip: skip }, { $limit: limit }],
      },
    });



    console.log("🔄 Final pipeline:", JSON.stringify(pipeline, null, 2));

    const results = await Candidates.aggregate(pipeline);

    const totalSubmission = results[0].metadata[0]?.total || 0;
    const submission = results[0].data;

    console.log("🔄 First few results with submittedAt:",
      submission.slice(0, 3).map(item => ({
        candidateID: item.candidateID,
        submittedAt: item.submission?.submittedAt,
        name: item.personalDetails?.firstName
      }))
    );





    res.status(200).json({
      success: true,
      message: "all submitted candidate fetched successfully",
      total: totalSubmission,
      page,
      totalPages: Math.ceil(totalSubmission / limit),
      results: submission,
    });
  } catch (error) {
    console.error("Error in get candidate submission:", error);
    return res.status(500).json({
      success: false,
      message: "Something went wrong while get candidate submission",
      error: error.message,
    });
  }
};

//* @Desc get candidate submissions with there status like hired,rejected,active & all submissions
//* @Route POST /api/v1/condidate/instantsubmit
//* @Access private, recruiter
const instantSubmit = async (req, res) => {
  try {
    const user = req.user;
    if (!user) {
      return res.status(400).json({
        success: false,
        message: "User not found",
      });
    }
    const recruiterId = user?._id;

    const { jobID, candidateID } = req.body;

    const amountDeducted = 2;

    if (!jobID || !candidateID) {
      return res.status(400).json({
        success: false,
        message: "JobId and candidateId are required.",
      });
    }

    const jobs = await job.findOne({ jobId: jobID });
    if (!jobs) {
      return res.status(400).json({
        success: false,
        message: "Job not found",
      });
    }

    const recruiter = await recruiterProfile.findOne({
      "user._id": recruiterId,
    });
    if (!recruiter) {
      return res.status(400).json({
        success: false,
        message: "Recruiter not found",
      });
    }

    // Check if the job is already selected by the recruiter
    if (
      recruiter?.jobsWorkingOn?.filter(
        (item) =>
          item.jobId.toString() == jobID?.toString() &&
          item.status == "assigned" &&
          item.isActive
      ).length > 0
    ) {
      return res.status(400).json({
        success: false,
        message: "Job is already selected by the recruiter",
      });
    }

    const userCoinBalances = await UserCoinBalance.findOne({
      userId: recruiter.user._id,
    });

    if (!userCoinBalances) {
      return res.status(400).json({
        success: false,
        message: "User coin balance not found",
      });
    }
    const candidiateDetails = await Candidates.findOne({
      candidateID: candidateID,
    });
    if (!candidiateDetails) {
      return res.status(400).json({
        success: false,
        message: "candidate not found",
      });
    }

    const workRequest = await candidateSubmission.findOne({
      "job.jobID": jobID,
      "candidate.candidateID": candidateID,
    });

    if (workRequest) {
      return res.status(400).json({
        success: false,
        message: "candidate is already submitted on this request.",
      });
    }

    // candidate submittions
    const CandidateSubmissionIntance = new candidateSubmission({
      isInstantSubmit: true,
      job: {
        _id: jobs._id,
        jobID: jobs.jobId,
      },
      submittedBy: {
        _id: user._id,
        userID: user.userId,
      },
      candidate: {
        _id: candidiateDetails._id,
        candidateID: candidiateDetails.candidateID,
      },
    });

    const submission = await CandidateSubmissionIntance.save();

    let eventDate = new Date();
    eventDate.setHours(0, 0, 0, 0);

    // add logs that status will be updated
    const submissionEventLog = new submissionLog({
      status: "submitted",
      submittedBy: user._id,
      submissionID: submission._id,
      eventDate: eventDate,
    });

    await submissionEventLog.save();

    // create coin transactions
    await CoinTransaction.create({
      userId: recruiter.user._id,
      relatedJobId: jobs._id,
      relatedSubmissionId: submission?._id,
      transactionType: "spent",
      quantity: 1,
      balanceBefore: userCoinBalances.currentBalance,
      balanceAfter: userCoinBalances.currentBalance - amountDeducted,
      status: "completed",
      spendType: "instantSubmit",
      amount: amountDeducted,
      createdAt: Date.now(),
    });

    // Update the recruiter to include the selected job
    recruiter.jobsWorkingOn.push({
      jobId: jobs._id,
      assignedBy: "self",
      isInstantSubmit: true,
      assignedAt: Date.now(),
    });
    await recruiter.save();

    // Update the coin balance
    userCoinBalances.currentBalance -= amountDeducted; // Deduct 1 coin for job selection
    userCoinBalances.totalSpent += amountDeducted; // Update total spent
    userCoinBalances.lastTransactionAt = Date.now();
    await userCoinBalances.save();

    res.status(200).json({
      success: true,
      message: "Job selected successfully",
    });
  } catch (error) {
    console.error("Error in candidate instant submit:", error);
    return res.status(500).json({
      success: false,
      message: "Something went wrong while candidate instant submit",
      error: error.message,
    });
  }
};

const UpdateCandidateStatus = async (req, res) => {
  try {
    const user = req.user;
    if (!user) {
      return res.status(400).json({
        success: false,
        message: "User not found",
      });
    }

    const { newstatus, submissionId, notes, eventdate } = req.body;

    if (!newstatus || !submissionId) {
      return res.status(400).json({
        success: false,
        message: "Submission ID and status are required.",
      });
    }

    if (!submissionEnums.includes(newstatus)) {
      return res.status(400).json({
        success: false,
        message: "Invalid submission status.",
      });
    }

    let query = {};

    if (mongoose.Types.ObjectId.isValid(submissionId)) {
      query._id = new mongoose.Types.ObjectId(submissionId);
    } else {
      query.submissionId = submissionId;
    }

    const isSubmissionWithStatus = await candidateSubmission.findOne({
      ...query,
      status: newstatus,
    });

    if (isSubmissionWithStatus) {
      return res.status(400).json({
        success: false,
        message: "Submission already has this status.",
      });
    }

    const submission = await candidateSubmission.findOneAndUpdate(
      query,
      { status: newstatus },
      { new: true }
    );

    if (!submission) {
      return res.status(400).json({
        success: false,
        message: "Submission not found.",
      });
    }

    let earnedTypeValue = null;

    // Coin logic
    if (["interviewing", "offer released"].includes(newstatus)) {
      let earnAmount = 0;

      if (newstatus === "interviewing") {
        earnAmount = 1;
        earnedTypeValue = "Interviewing";

        await recruiterProfile.findOneAndUpdate(
          {
            "user._id": new mongoose.Types.ObjectId(
              submission?.submittedBy?._id
            ),
            "jobsWorkingOn.jobId": new mongoose.Types.ObjectId(
              submission?.job?._id
            ),
          },
          {
            $set: {
              "jobsWorkingOn.$.isSlotEmpty": true,
            },
          },
          { new: true }
        );
      }

      if (newstatus === "offer released") {
        earnAmount = 3;
        earnedTypeValue = "Hired";
      }

      const userCoin = await UserCoinBalance.findOneAndUpdate(
        { userId: new mongoose.Types.ObjectId(submission?.submittedBy?._id) },
        { $inc: { currentBalance: earnAmount, totalEarned: earnAmount } },
        { new: true }
      );

      if (!userCoin) {
        return res.status(400).json({
          success: false,
          message: "User coin balance not found.",
        });
      }

      const coinTransaction = new CoinTransaction({
        userId: submission?.submittedBy?._id,
        relatedJobId: submission?.job?._id,
        relatedSubmissionId: submission._id,
        transactionType: "earned",
        earnedType: earnedTypeValue,
        amount: earnAmount,
        quantity: earnAmount,
        balanceBefore: userCoin.currentBalance - earnAmount,
        balanceAfter: userCoin.currentBalance,
      });

      await coinTransaction.save();
    }

    // Event Date
    let eventDate = eventdate ? new Date(eventdate) : new Date();
    eventDate.setHours(0, 0, 0, 0);

    const submissionEventLog = new submissionLog({
      status: newstatus,
      submittedBy: user._id,
      submissionID: submission._id,
      eventDate,
      notes,
    });

    await submissionEventLog.save();

    res.status(200).json({
      success: true,
      submission,
      earnedType: earnedTypeValue,
      message: "Candidate status updated successfully.",
    });
  } catch (error) {
    console.error("Error updating candidate status:", error);
    return res.status(500).json({
      success: false,
      message: "Something went wrong while updating candidate status.",
      error: error.message,
    });
  }
};

//* @Desc get candidate timeline
//* @Route GET /api/v1/condidate/candidate-timeline/:candidiateID
//* @Access private,
const getCandidateTimeline = async (req, res) => {
  try {
    const user = req.user;
    if (!user) {
      return res.status(400).json({
        success: false,
        message: "User not found",
      });
    }

    const { submissionID } = req.params;

    if (!submissionID) {
      return res.status(400).json({
        success: false,
        message: "submission id not found",
      });
    }

    const results = await submissionLog.aggregate([
      {
        $lookup: {
          from: "candidatesubmissions",
          localField: "submissionID",
          foreignField: "_id",
          as: "submissionID",
        },
      },
      {
        $unwind: {
          path: "$submissionID",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $match: {
          "submissionID.submissionId": submissionID,
        },
      },
      {
        $sort: {
          eventDate: -1,
        },
      },
    ]);

    res.status(200).json({
      success: true,
      message: "candidate timeline successfully",
      data: results,
    });
  } catch (error) {
    console.error("Error in candidate timeline:", error);
    return res.status(500).json({
      success: false,
      message: "Something went wrong while candidate timeline",
      error: error.message,
    });
  }
};

//* @Desc get candidate timeline
//* @Route GET /api/v1/condidate/submission-stats
//* @Access private,
const submissionStats = async (req, res) => {
  try {
    const user = req.user;
    if (!user) {
      return res.status(400).json({
        success: false,
        message: "User not found",
      });
    }

    const submission = await submissionLog.aggregate([
      {
        $lookup: {
          from: "candidatesubmissions",
          localField: "submissionID",
          foreignField: "_id",
          as: "submissionID",
        },
      },
      {
        $unwind: "$submissionID",
      },
      {
        $lookup: {
          from: "jobs",
          localField: "submissionID.job._id",
          foreignField: "_id",
          as: "job",
        },
      },
      {
        $unwind: "$job",
      },
      {
        $match: {
          "job.accountManager.userID": user.userId,
          "job.jobStatus": "Active",
          "job.isDeleted": false,
        },
      },
      {
        $facet: {
          responseRate: [
            {
              $match: {
                status: {
                  $in: ["reviewing", "submitted"],
                },
              },
            },
            {
              $group: {
                _id: "$submissionID._id",
                submitted: {
                  $min: {
                    $cond: [
                      {
                        $eq: ["$status", "submitted"],
                      },
                      "$createdAt",
                      null,
                    ],
                  },
                },
                reviewing: {
                  $min: {
                    $cond: [
                      {
                        $eq: ["$status", "reviewing"],
                      },
                      "$createdAt",
                      null,
                    ],
                  },
                },
              },
            },
            {
              $addFields: {
                timeDiff: {
                  $cond: [
                    {
                      $and: ["$submitted", "$reviewing"],
                    },
                    {
                      $round: [
                        {
                          $divide: [
                            {
                              $subtract: ["$reviewing", "$submitted"],
                            },
                            1000 * 60,
                          ],
                        },
                        3,
                      ],
                    },
                    null,
                  ],
                },
              },
            },
            {
              $match: {
                timeDiff: { $ne: null },
              },
            },
            {
              $group: {
                _id: null,
                averageTimeDiffMs: {
                  $avg: "$timeDiff",
                },
              },
            },
            {
              $project: {
                _id: 0,
                averageTimeDiffMs: 1,
              },
            },
          ],
          submissionToInterview: [
            {
              $match: {
                status: {
                  $in: ["submitted", "interviewing"],
                },
              },
            },
            {
              $group: {
                _id: "$submissionID._id",
                submitted: {
                  $sum: {
                    $cond: [
                      {
                        $eq: ["$status", "submitted"],
                      },
                      1,
                      0,
                    ],
                  },
                },
                interviewing: {
                  $sum: {
                    $cond: [
                      {
                        $eq: ["$status", "interviewing"],
                      },
                      1,
                      0,
                    ],
                  },
                },
              },
            },
            {
              $addFields: {
                interviewRate: {
                  $cond: [
                    { $eq: ["$submitted", 0] },
                    0,
                    {
                      $round: [
                        {
                          $multiply: [
                            {
                              $divide: ["$interviewing", "$submitted"],
                            },
                            100,
                          ],
                        },
                        2,
                      ],
                    },
                  ],
                },
              },
            },
            {
              $project: {
                _id: 0,
                interviewRate: 1,
              },
            },
          ],
          InterviewToOffer: [
            {
              $match: {
                status: {
                  $in: ["offer released", "interviewing"],
                },
              },
            },
            {
              $group: {
                _id: "$submissionID._id",
                offer: {
                  $sum: {
                    $cond: [
                      {
                        $eq: ["$status", "offer released"],
                      },
                      1,
                      0,
                    ],
                  },
                },
                interviewing: {
                  $sum: {
                    $cond: [
                      {
                        $eq: ["$status", "interviewing"],
                      },
                      1,
                      0,
                    ],
                  },
                },
              },
            },
            {
              $addFields: {
                offerRate: {
                  $cond: [
                    { $eq: ["$interviewing", 0] },
                    0,
                    {
                      $round: [
                        {
                          $multiply: [
                            {
                              $divide: ["$offer", "$interviewing"],
                            },
                            100,
                          ],
                        },
                        2,
                      ],
                    },
                  ],
                },
              },
            },
            {
              $project: {
                _id: 0,
                offerRate: 1,
              },
            },
          ],
        },
      },
      {
        $unwind: {
          path: "$InterviewToOffer",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $unwind: {
          path: "$submissionToInterview",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $unwind: {
          path: "$responseRate",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $project: {
          "Interviewing to Offer": "$InterviewToOffer.offerRate",
          "Submission to Interviewing": "$submissionToInterview.interviewRate",
          "Response Rate": "$responseRate.averageTimeDiffMs",
        },
      },
    ]);

    const jobs = await job.aggregate([
      {
        $match: {
          "accountManager.userID": user.userId,
          jobStatus: "Active",
          isDeleted: false,
        },
      },
      {
        $lookup: {
          from: "candidatesubmissions",
          localField: "_id",
          foreignField: "job._id",
          as: "candidatesubmissions",
        },
      },
      {
        $addFields: {
          candidatesubmissions: {
            $size: "$candidatesubmissions",
          },
        },
      },
      {
        $group: {
          _id: null,
          totalJobs: { $sum: 1 },
          submissionJobs: {
            $sum: {
              $cond: [{ $gt: ["$candidatesubmissions", 0] }, 1, 0],
            },
          },
        },
      },
      {
        $addFields: {
          submissionPercentage: {
            $cond: [
              { $eq: ["$totalJobs", 0] },
              0,
              {
                $multiply: [
                  {
                    $divide: ["$submissionJobs", "$totalJobs"],
                  },
                  100,
                ],
              },
            ],
          },
        },
      },
      {
        $project: {
          _id: 0,
          submissionPercentage: 1,
        },
      },
    ]);

    const data = {
      "Coverage Ratio": jobs[0]?.submissionPercentage ?? 0,
      "Interviewing to Offer": submission[0]?.["Interviewing to Offer"] ?? 0,
      "Submission to Interviewing":
        submission[0]?.["Submission to Interviewing"] ?? 0,
      "Response Rate": submission[0]?.["Response Rate"] ?? 0,
    };

    return res.status(200).json({
      success: true,
      data,
    });
  } catch (error) {
    console.error("Error in candidate timeline:", error);
    return res.status(500).json({
      success: false,
      message: "Something went wrong while candidate timeline",
      error: error.message,
    });
  }
};

//* @Desc get maximum experience value from all candidates
//* @Route GET /api/v1/condidate/max-experience
//* @Access private
const getMaxExperience = async (req, res) => {
  try {
    const user = req.user;
    if (!user) {
      return res.status(400).json({
        success: false,
        message: "User not found",
      });
    }
    // Only consider candidates created by this user (recruiter)
    const result = await Candidates.aggregate([
      { $match: { "createdBy.userID": user.userId } },
      {
        $addFields: {
          expValue: {
            $toInt: {
              $arrayElemAt: [
                { $split: ["$skillsAndExperience.totalYearsOfExperience", " "] },
                0
              ]
            }
          }
        }
      },
      {
        $group: {
          _id: null,
          maxExperience: { $max: "$expValue" }
        }
      }
    ]);
    const maxExperience = result[0]?.maxExperience || 12; // fallback to 12 if no data
    res.status(200).json({ success: true, maxExperience });
  } catch (error) {
    console.error("Error in getMaxExperience:", error);
    return res.status(500).json({
      success: false,
      message: "Something went wrong while fetching max experience",
      error: error.message,
    });
  }
};

module.exports = {
  getCandidate,
  getAllCandidate,
  AddCandidate,
  updateCandidate,
  removeCandidate,
  Licensing,
  Certification,
  Education,
  WorkHistory,
  skillsAndExperience,
  HealthAndCompliance,
  SubmissionDetails,
  DocumentAttachment,
  candidateJobSubmissions,
  getSubmissions,
  instantSubmit,
  UpdateCandidateStatus,
  getCandidateTimeline,
  submissionStats,
  getMaxExperience,
};
