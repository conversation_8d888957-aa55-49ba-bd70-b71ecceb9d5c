const express = require("express");
const router = express.Router();

const {
  getCandidate,
  AddCandidate,
  Licensing,
  Certification,
  Education,
  WorkHistory,
  skillsAndExperience,
  SubmissionDetails,
  HealthAndCompliance,
  DocumentAttachment,
  getAllCandidate,
  candidateJobSubmissions,
  updateCandidate,
  removeCandidate,
  getSubmissions,
  instantSubmit,
  UpdateCandidateStatus,
  getCandidateTimeline,
  submissionStats,
  getMaxExperience,
} = require("../controllers/Candidate");

const { auth, authorize } = require("../middlewares/auth");
const upload = require("../middlewares/uploadMiddleware");
const { getSubmissionDetails } = require("../controllers/JobSubmission");

router.get("/getallcandidate", auth, authorize("recruiter"), getAllCandidate);
router.get(
  "/getcandidate/:candidateID",
  auth,
  authorize("recruiter", "headAccountManager", "accountManager"),
  getCandidate
);
// add new candidate
router.post(
  "/addcandidate/personalDetails",
  auth,
  authorize("recruiter"),
  AddCandidate
);
router.patch(
  "/addcandidate/licensing/:candidateID",
  auth,
  authorize("recruiter"),
  Licensing
);
router.patch(
  "/addcandidate/certification/:candidateID",
  auth,
  authorize("recruiter"),
  Certification
);
router.patch(
  "/addcandidate/education/:candidateID",
  auth,
  authorize("recruiter"),
  Education
);
router.patch(
  "/addcandidate/workhistory/:candidateID",
  auth,
  authorize("recruiter"),
  WorkHistory
);
router.patch(
  "/addcandidate/skillsandexperience/:candidateID",
  auth,
  authorize("recruiter"),
  skillsAndExperience
);
router.patch(
  "/addcandidate/healthandcompliance/:candidateID",
  auth,
  authorize("recruiter"),
  HealthAndCompliance
);
router.patch(
  "/addcandidate/submissiondetails/:candidateID",
  auth,
  authorize("recruiter"),
  SubmissionDetails
);

router.patch(
  "/addcandidate/documentattachment/:candidateID",
  auth,
  authorize("recruiter"),
  upload.any(),
  DocumentAttachment
);

// update candidate
router.patch(
  "/updatecandidate/personaldetails/:candidateID",
  auth,
  authorize("recruiter"),
  updateCandidate
);

// remove candidate
router.delete(
  "/removecandidate/:candidateID",
  auth,
  authorize("recruiter"),
  removeCandidate
);

// candidate job submissions
router.post(
  "/candidatejobsubmissions",
  auth,
  authorize("recruiter"),
  candidateJobSubmissions
);

// get candidate submissions with there status like hired,rejected,active & all submissions
router.get("/getsubmissions", auth, authorize("recruiter"), getSubmissions);

// candidate want to submit the job instant way with more extra  coin
router.post("/instantsubmit", auth, authorize("recruiter"), instantSubmit);

// update the candidate status by head account manager and am & add timeline
router.post(
  "/candidate-status-update",
  auth,
  authorize("accountManager", "headAccountManager"),
  UpdateCandidateStatus
);

// Get Submission Details
router.get(
  "/submission-details",
  auth,
  authorize("accountManager", "headAccountManager"),
  getSubmissionDetails
);

router.get("/candidate-timeline/:submissionID", auth, getCandidateTimeline);

router.get(
  "/submission-stats",
  auth,
  authorize("accountManager"),
  submissionStats
);

// get maximum experience value from all candidates
router.get("/max-experience", auth, authorize("recruiter"), getMaxExperience);

module.exports = router;
