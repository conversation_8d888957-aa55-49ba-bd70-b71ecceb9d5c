import React, { useEffect, useState } from "react";
import TabNav from "../../components/common/TabNav/TabNav";
import searchImg from "../../assets/icons/search.svg";
import { useLocation, useNavigate } from "react-router-dom";
import CandidateTable from "../../components/core/recruiter/candidates/CandidateTable";
import AppButton from "../../components/common/Button/AppButton";
import { getSubmission } from "../../services/operations/candidateAPI";
import { getMaxExperience } from "../../services/operations/experienceAPI";
import { JobStatusOptions } from "../../utils/JobStatusOptions";
import ThreeDot from "../../components/common/Button/ThreeDot";
import filterImg from "../../assets/icons/filter.svg";
import sortingImg from "../../assets/icons/sorting.svg";
import LocationDropdown from "../../components/core/recruiter/candidates/LocationDropdown";
import StatusDropdown from "../../components/core/recruiter/candidates/StatusDropdown";
import JobTypeDropdown from "../../components/core/recruiter/candidates/JobTypeDropdown";
import ExperienceLevelDropdown from "../../components/core/recruiter/candidates/ExperienceLevelDropdown";
import FilterBar from "../../components/core/recruiter/candidates/FilterBar";
import SearchDropdown from "../../components/core/recruiter/candidates/SearchDropdown";
import SubmissionDateSortPill from "../../components/core/recruiter/candidates/SubmissionDateSortPill";

const Candidates = () => {
  const { search, pathname } = useLocation();
  const queryParams = new URLSearchParams(search);
  const currentTabs = queryParams.get("tabs");

  const [active, setActive] = useState(0);
  const [showFilter, setShowFilter] = useState(false);
  const [filterValues, setFilterValues] = useState({});
  const [locationSelected, setLocationSelected] = useState([]);
  const [statusSelected, setStatusSelected] = useState([]);
  const [showSort, setShowSort] = useState(false);
  const [sortValue, setSortValue] = useState("recent");
  const [jobTypeSelected, setJobTypeSelected] = useState([]);
  const [experienceLevelSelected, setExperienceLevelSelected] = useState([1, 12]);
  const [maxExperience, setMaxExperience] = useState(12);
  const [showSearch, setShowSearch] = useState(false);
  const [searchResults, setSearchResults] = useState(null);
  const [appliedFilters, setAppliedFilters] = useState({});
  const [isFiltersApplied, setIsFiltersApplied] = useState(false);
  const navigate = useNavigate();

  const tabs = [
    { name: <span>Active Submissions</span>, css: "" },
    { name: <span>Hired Candidates</span>, css: "" },
    { name: <span>Rejected Candidates</span>, css: "" },
    { name: <span>All Submissions</span>, css: "" },
  ];

  const filterOptions = {
    0: [
      {
        label: "Location",
        component: (
          <LocationDropdown
            selected={locationSelected}
            setSelected={setLocationSelected}
          />
        ),
        selected: locationSelected,
        placeholder: "Select Location",
        key: "location",
      },
      {
        label: "Status",
        component: (
          <StatusDropdown
            selected={statusSelected}
            setSelected={setStatusSelected}
          />
        ),
        selected: statusSelected,
        placeholder: "Select Status",
        key: "status",
      },
    ],
    1: [
      {
        label: "Location",
        component: (
          <LocationDropdown
            selected={locationSelected}
            setSelected={setLocationSelected}
          />
        ),
        selected: locationSelected,
        placeholder: "Select Location",
        key: "location1",
      },
      {
        label: "Status",
        component: (
          <StatusDropdown
            selected={statusSelected}
            setSelected={setStatusSelected}
          />
        ),
        selected: statusSelected,
        placeholder: "Select Status",
        key: "status1",
      },
      {
        label: "Job Type",
        component: (
          <JobTypeDropdown
            selected={jobTypeSelected}
            setSelected={setJobTypeSelected}
          />
        ),
        selected: jobTypeSelected,
        placeholder: "Select Job Type",
        key: "jobType1",
      },
      {
        label: "Experience Level",
        component: (
          <ExperienceLevelDropdown
            selected={experienceLevelSelected}
            setSelected={setExperienceLevelSelected}
            min={1}
            max={maxExperience}
          />
        ),
        selected: experienceLevelSelected,
        placeholder: "Select Experience Level",
        key: "experienceLevel1",
      },
    ],
    3: [
      {
        label: "Location",
        component: (
          <LocationDropdown
            selected={locationSelected}
            setSelected={setLocationSelected}
          />
        ),
        selected: locationSelected,
        placeholder: "Select Location",
        key: "location3",
      },
      {
        label: "Status",
        component: (
          <StatusDropdown
            selected={statusSelected}
            setSelected={setStatusSelected}
          />
        ),
        selected: statusSelected,
        placeholder: "Select Status",
        key: "status3",
      },
      {
        label: "Job Type",
        component: (
          <JobTypeDropdown
            selected={jobTypeSelected}
            setSelected={setJobTypeSelected}
          />
        ),
        selected: jobTypeSelected,
        placeholder: "Select Job Type",
        key: "jobType3",
      },
      {
        label: "Experience Level",
        component: (
          <ExperienceLevelDropdown
            selected={experienceLevelSelected}
            setSelected={setExperienceLevelSelected}
            min={1}
            max={maxExperience}
          />
        ),
        selected: experienceLevelSelected,
        placeholder: "Select Experience Level",
        key: "experienceLevel3",
      },
    ],
  };

  useEffect(() => {
    navigate(`${pathname}?${currentTabs ? `tabs=${currentTabs}` : "tabs=0"}`, {
      replace: true,
    });
  }, []);

  useEffect(() => {
    if (currentTabs) {
      setActive(Number(currentTabs));
      setShowFilter(false); // Hide filter bar when switching tabs
      setAppliedFilters({}); // Clear applied filters on tab switch
      setIsFiltersApplied(false);
    } else {
      setActive(0);
      setShowFilter(false);
      setShowSort(false);
      setFilterValues({});
      setAppliedFilters({});
      setIsFiltersApplied(false);
    }
  }, [currentTabs, pathname]);

  useEffect(() => {
    async function fetchMaxExp() {
      const maxExp = await getMaxExperience();
      setMaxExperience(maxExp);
      // If current selected is out of new max, reset
      setExperienceLevelSelected(([min, max]) => [min, Math.min(max, maxExp)]);
    }
    fetchMaxExp();
  }, []);

  function updateTabs(tabIndex) {
    navigate(`${pathname}?tabs=${tabIndex}`, { replace: true });
    setActive(tabIndex);
    setShowFilter(false); // Hide filter when switching tabs
    setShowSort(false); // Hide sorting when switching tabs
    setShowSearch(false); // Hide search when switching tabs

    // Clear all filter and sorting values when switching tabs
    setFilterValues({});
    setLocationSelected([]);
    setStatusSelected([]);
    setJobTypeSelected([]);
    setExperienceLevelSelected([1, 12]);
    setSortValue("recent");
    setSearchResults(null); // Clear search results

    // Clear applied filters when switching tabs
    setAppliedFilters({});
    setIsFiltersApplied(false);
  }
  async function getSubmissionDetail(
    submissionType = "activeSubmission",
    page,
    limit,
    filters = {}
  ) {
    const result = await getSubmission(submissionType, page, limit, filters);
    return {
      data: result.results,
      totalResults: result.total,
      totalPages: result.totalPages,
      page: result.page,
      limit: result.limit, // Fixed: was result.total, should be result.limit
    };
  }

  // Prevent navigation/redirect on filter/sort click
  function handleFilterClick(e) {
    e?.stopPropagation?.();
    setShowFilter((prev) => !prev);
  }
  function handleSortClick(e) {
    e?.stopPropagation?.();
    setShowSort((prev) => !prev);
  }
  function handleClearFilter() {
    // Clear all filter values
    setFilterValues({});
    setLocationSelected([]);
    setStatusSelected([]);
    setJobTypeSelected([]);
    setExperienceLevelSelected([1, 12]);

    // Clear sorting values
    setSortValue("recent");

    // Clear applied filters
    setAppliedFilters({});
    setIsFiltersApplied(false);

    // Hide both filter and sorting sections
    setShowFilter(false);
    setShowSort(false);
  }
  function handleApplyFilter() {
    // Build filter object based on current selections
    const filters = {};

    // Location filter
    if (locationSelected && locationSelected.length > 0) {
      filters.location = locationSelected;
    }

    // Status filter
    if (statusSelected && statusSelected.length > 0) {
      filters.status = statusSelected;
    }

    // Job Type filter
    if (jobTypeSelected && jobTypeSelected.length > 0) {
      filters.jobType = jobTypeSelected;
    }

    // Experience level filter
    if (experienceLevelSelected && experienceLevelSelected.length === 2) {
      const [min, max] = experienceLevelSelected;
      if (min !== 1 || max !== 12) { // Only apply if not default range
        filters.experienceLevel = [`${min}-${max}`]; // Send as array for consistency
      }
    }

    // Sorting filter
    if (sortValue) {
      filters.sortBy = sortValue === "recent" ? "newest" : "oldest";
    } else {
      filters.sortBy = "newest"; // Default sorting
    }

    console.log("🔄 Frontend sending sortBy:", filters.sortBy, "from sortValue:", sortValue);
    console.log("🔄 All filters being sent:", filters);
    alert(`Frontend: sortBy=${filters.sortBy}, sortValue=${sortValue}`);

    // Add timestamp to prevent caching
    filters._timestamp = Date.now();

    // Store applied filters and mark as applied
    setAppliedFilters(filters);
    setIsFiltersApplied(true);

    // Hide filter and sort sections after applying
    setShowFilter(false);
    setShowSort(false);
  }

  function handleSearchClick() {
    setShowSearch((prev) => !prev);
  }

  function handleSearch(searchData) {

    setSearchResults(searchData);
    // Implement search logic here
  }

  return (
    <>
      <TabNav
        nav={tabs}
        active={active}
        setActive={updateTabs}
        rightSidebar={
          <div className="flex gap-2 items-center relative">
            <div className="relative">
              <img
                src={searchImg}
                alt="Search"
                className="w-8 h-8 cursor-pointer"
                onClick={handleSearchClick}
              />
              <SearchDropdown
                isOpen={showSearch}
                onClose={() => setShowSearch(false)}
                onSearch={handleSearch}
                activeTab={active}
              />
            </div>
            <ThreeDot
              dropdownSize="w-56"
              buttonDropDown={(onClose) => (
                <div className="min-w-[200px]">
                  <div className="flex items-center justify-between px-4 pt-3 pb-2">
                    <span className="font-medium text-gray-900 text-base">
                      View Option
                    </span>
                    <button
                      className="text-gray-400 hover:text-gray-700 text-lg font-bold focus:outline-none"
                      onClick={(e) => {
                        e.stopPropagation();
                        onClose();
                      }}
                      aria-label="Close"
                      type="button"
                    >
                      ×
                    </button>
                  </div>
                  <hr className="my-1 border-gray-200" />
                  <ul className="py-0" role="none">
                    {active !== 2 && (
                      <li>
                        <div
                          className="flex cursor-pointer items-center justify-start gap-2 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 "
                          role="menuitem"
                          onClick={handleFilterClick}
                        >
                          <img src={filterImg} alt="Filter" className="w-4 h-4" />
                          Filter
                        </div>
                      </li>
                    )}
                    <li>
                      <div
                        className="flex cursor-pointer items-center justify-start gap-2 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 "
                        role="menuitem"
                        onClick={handleSortClick}
                      >
                        <img src={sortingImg} alt="Sorting" className="w-4 h-4" />
                        Sorting
                      </div>
                    </li>
                  </ul>
                </div>
              )}
            />

            {active === 3 && (
              <AppButton
                label="+ Add Candidate"
                onClick={() => navigate("/candidate/addcandidate")}
                variant="primary"
              />
            )}
          </div>
        }
      />
      <div className="overflow-x-auto rounded-lg mt-2 border border-gray-200 shadow-sm">
        {showFilter && (
          <FilterBar
            filters={filterOptions[active] || []}
            onChange={setFilterValues}
            onClear={() => {}} // Empty function since we'll handle this below
            onApply={() => {}} // Empty function since we'll handle this below
            rightAlignActions={false} // Don't show actions in FilterBar
          />
        )}

        {showSort && (
          <div className="px-4 py-3 bg-gray-50 border-b border-gray-200">
            <SubmissionDateSortPill
              key={sortValue}
              selected={sortValue}
              setSelected={setSortValue}
              onClose={() => setShowSort(false)}
            />
          </div>
        )}

        {(showFilter || showSort) && (
          <div className="px-4 py-2 bg-white border-b border-gray-200 flex justify-end">
            <div className="flex items-center gap-2">
              <span className="text-blue-500 cursor-pointer text-xs" onClick={handleClearFilter}>
                Clear all
              </span>
              <button
                className="px-4 py-1 rounded bg-green-500 text-white text-sm font-medium hover:bg-green-600"
                onClick={handleApplyFilter}
              >
                Apply
              </button>
            </div>
          </div>
        )}

        {active === 0 && (
          <CandidateTable
            key={`active-submissions-${JSON.stringify(appliedFilters)}-${isFiltersApplied}`}
            columns={[
              "Name",
              "Job Title",
              "Job Id",
              "Location",
              "Submission Date",
              "Email",
              "Status",
              "",
            ]}
            getData={async (page, limit) => {
              const data = await getSubmissionDetail(
                "activeSubmission",
                page,
                limit,
                isFiltersApplied ? appliedFilters : {}
              );



              const resData = data?.data?.map((item) => {
                return {
                  id: item?.candidateID,
                  name:
                    item?.personalDetails?.firstName +
                    " " +
                    item?.personalDetails?.lastName,
                  jobTitle: item?.submission?.job?.jobTitle
                    ? item?.submission?.job?.jobTitle
                    : "-",
                  jobId: item?.submission?.job?.jobId
                    ? item?.submission?.job?.jobId
                    : "-",
                  location:
                    item?.submission?.job?.location?.country?.split("-")[0],
                  submissionDate: new Date(
                    item?.submission?.submittedAt
                  )?.toLocaleString(),
                  email: item?.personalDetails?.emailAddress,
                  status: item?.status,
                  submissionId: item?.submission?.submissionId,
                };
              });
              // Debug: Check what we're sending to CandidateTable
              const result = {
                resData,
                totalResults: data?.totalResults,
                totalPages: data?.totalPages,
                page: data?.page,
                limit: data?.limit,
              };



              return result;
            }}
            dropdownkey="status"
            isDropDownDisable={true}
            dropdownItems={[
              ...JobStatusOptions,
              {
                value: "Talent Pool",
                label: "Talent Pool",
                color: "bg-[#CEFAFE] text-[#00B8DB]",
              },
            ]}
          />
        )}
        {active === 1 && (
          <CandidateTable
            key={`hired-candidates-${JSON.stringify(appliedFilters)}-${isFiltersApplied}`}
            columns={[
              "Name",
              "Job Title",
              "Job Id",
              "Email",
              "Location",
              "Job Type",
              "Experience",
              "",
            ]}
            getData={async (page, limit) => {
              const data = await getSubmissionDetail(
                "hired",
                page,
                limit,
                isFiltersApplied ? appliedFilters : {}
              );
              const resData = data?.data?.map((item) => {
                return {
                  id: item?.candidateID,
                  name:
                    item?.personalDetails?.firstName +
                    " " +
                    item?.personalDetails?.lastName,
                  jobTitle: item?.submission?.job?.jobTitle
                    ? item?.submission?.job?.jobTitle
                    : "-",
                  jobId: item?.submission?.job?.jobId
                    ? item?.submission?.job?.jobId
                    : "-",
                  email: item?.personalDetails?.emailAddress,
                  location: item?.submission?.job?.location?.country
                    ? item?.submission?.job?.location?.country?.split("-")[0]
                    : "-",
                  jobType: item?.submission?.job?.jobType,

                  experience: item?.submission?.job?.experience?.min
                    ? item?.submission?.job?.experience?.min +
                      " - " +
                      item?.submission?.job?.experience?.max +
                      " " +
                      item?.submission?.job?.experience?.unit
                    : "-",
                  submissionId: item?.submission?.submissionId,
                };
              });
              return {
                resData,
                totalResults: data?.totalResults,
                totalPages: data?.totalPages,
                page: data?.page,
                limit: data?.limit,
              };
            }}
          />
        )}
        {active === 2 && (
          <CandidateTable
            key={`rejected-candidates-${active}`}
            columns={["Name", "Job Title", "Job Id", "Email", "Reason", ""]}
            getData={async (page, limit) => {
              const data = await getSubmissionDetail("rejected", page, limit);
              const resData = data?.data?.map((item) => {
                return {
                  id: item?.candidateID,
                  name:
                    item?.personalDetails?.firstName +
                    " " +
                    item?.personalDetails?.lastName,
                  jobTitle: item?.submission?.job?.jobTitle
                    ? item?.submission?.job?.jobTitle
                    : "-",
                  jobId: item?.submission?.job?.jobId
                    ? item?.submission?.job?.jobId
                    : "-",
                  email: item?.personalDetails?.emailAddress,
                  reason: item?.submission?.job?.notes
                    ? item?.submission?.job?.notes
                    : "-",
                  submissionId: item?.submission?.submissionId,
                };
              });
              return {
                resData,
                totalResults: data?.totalResults,
                totalPages: data?.totalPages,
                page: data?.page,
                limit: data?.limit,
              };
            }}
          />
        )}
        {active === 3 && (
          <CandidateTable
            key={`all-submissions-${JSON.stringify(appliedFilters)}-${isFiltersApplied}`}
            columns={[
              "Name",
              "Job Id",
              "Job Title",
              "Location",
              "Job Type",
              "Mobile Number",
              "Experience",
              "Email",
              "Status",
              " ",
            ]}
            getData={async (page, limit) => {
              const data = await getSubmissionDetail(
                "any",
                page,
                limit,
                isFiltersApplied ? appliedFilters : {}
              );
              const resData = data?.data?.map((item) => {
                return {
                  id: item?.candidateID,
                  name:
                    item?.personalDetails?.firstName +
                    " " +
                    item?.personalDetails?.lastName,

                  jobId: item?.submission?.job?.jobId
                    ? item?.submission?.job?.jobId
                    : "-",
                  jobTitle: item?.submission?.job?.jobTitle
                    ? item?.submission?.job?.jobTitle
                    : "-",
                  location: item?.submission?.job?.location?.country
                    ? item?.submission?.job?.location?.country?.split("-")[0]
                    : "-",

                  jobType: item?.submission?.job?.jobType
                    ? item?.submission?.job?.jobType
                    : "-",

                  mobileNo: `${
                    item?.personalDetails?.phoneCountryCode
                      ? "+" + item?.personalDetails?.phoneCountryCode
                      : ""
                  } ${
                    item?.personalDetails?.phoneNumber
                      ? item?.personalDetails?.phoneNumber
                      : ""
                  }`,

                  experience: item?.submission?.job?.experience?.min
                    ? item?.submission?.job?.experience?.min +
                      " - " +
                      item?.submission?.job?.experience?.max +
                      " " +
                      item?.submission?.job?.experience?.unit
                    : "-",

                  email: item?.personalDetails?.emailAddress,
                  status: item?.status,
                  submissionId: item?.submission?.submissionId,
                };
              });
              return {
                resData,
                totalResults: data?.totalResults,
                totalPages: data?.totalPages,
                page: data?.page,
                limit: data?.limit,
              };
            }}
            // dropdownwidth="w-10"
            dropdownkey="status"
            isDropDownDisable={true}
            dropdownItems={[
              ...JobStatusOptions,
              {
                value: "Talent Pool",
                label: "Talent Pool",
                color: "bg-[#CEFAFE] text-[#00B8DB]",
              },
            ]}
          />
        )}
      </div>
    </>
  );
};

export default Candidates;
