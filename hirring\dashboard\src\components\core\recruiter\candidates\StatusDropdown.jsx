import React from "react";

const statusOptions = [
  { value: "submitted", label: "Submitted" },
  { value: "reviewing", label: "Reviewing" },
  { value: "submitted to client", label: "Submitted to Client" },
  { value: "selected", label: "Selected" },
  { value: "interviewing", label: "Interviewing" },
  { value: "offer extended", label: "Offer Extended" },
  { value: "offer accepted", label: "Offer Accepted" },
  { value: "hired-under guarantee period", label: "Hired (Under Guarantee)" },
  { value: "rejected", label: "Rejected" },
  { value: "offer rejected", label: "Offer Rejected" },
];

const StatusDropdown = ({ selected, setSelected, onClose }) => {
  function selectStatus(statusValue) {
    // Only allow single selection - if same status is clicked, deselect it
    setSelected((prev) =>
      prev.includes(statusValue) ? [] : [statusValue]
    );
  }

  return (
    <div className="bg-white rounded-xl shadow-lg border p-4 w-72 max-w-full relative">
      <div className="flex items-center justify-between mb-2">
        <span className="font-semibold text-gray-800 text-base">Status</span>
        <button
          className="text-gray-400 hover:text-gray-700 text-lg"
          onClick={onClose}
          aria-label="Close"
        >
          ×
        </button>
      </div>
      <div className="border-b mb-2" />
      <div className="flex flex-col gap-2 max-h-60 overflow-y-auto">
        {statusOptions.map((status) => (
          <label
            key={status.value}
            className={`flex items-center gap-2 text-sm cursor-pointer px-2 py-1 rounded ${
              selected.includes(status.value)
                ? "bg-blue-50 text-blue-700 font-medium"
                : "hover:bg-gray-50"
            }`}
          >
            <input
              type="checkbox"
              checked={selected.includes(status.value)}
              onChange={() => selectStatus(status.value)}
              className="accent-blue-600 w-4 h-4 rounded border-gray-300"
            />
            <span className="truncate">{status.label}</span>
          </label>
        ))}
      </div>
    </div>
  );
};

export default StatusDropdown;
